# 批量查询多个酒店最佳价格功能

## 功能概述

新增了一个批量查询多个酒店最佳价格的API接口，用于快速获取多个酒店的最低价格信息。该功能特别适用于酒店列表页面、搜索结果展示和价格比较场景。

## 主要特性

### 🚀 核心功能
- **批量查询**: 一次请求可查询最多20个酒店的价格
- **最佳价格**: 自动筛选每个酒店的最低价格
- **并发处理**: 并发查询多个酒店，提高响应速度
- **部分成功**: 即使部分酒店查询失败，也会返回成功的结果
- **货币转换**: 自动转换为人民币显示

### 💡 智能优化
- **缓存机制**: 相同参数的查询会使用缓存结果
- **错误容错**: 单个酒店查询失败不影响整体结果
- **价格筛选**: 智能筛选有效价格，过滤异常数据
- **会员优先**: 优先显示会员价格（如果可用）

### 🔧 技术特性
- **参数验证**: 完整的请求参数验证
- **日志记录**: 详细的查询日志和统计信息
- **性能监控**: 查询性能和成功率监控
- **配置灵活**: 丰富的配置选项

## 文件结构

```
├── app/Services/SabreAvailabilityService.php     # 新增批量查询方法
├── app/Http/Controllers/V1/SabreController.php   # 新增控制器方法
├── routes/v1.php                                 # 新增路由
├── config/sabre_batch_query.php                 # 批量查询配置
├── docs/api/multiple-hotels-best-prices.md      # API文档
├── examples/multiple_hotels_best_prices_example.php # 使用示例
├── tests/Feature/MultipleHotelsBestPricesTest.php   # 功能测试
└── README_MULTIPLE_HOTELS_BEST_PRICES.md        # 本文件
```

## 新增的代码

### 1. SabreAvailabilityService 新增方法

<augment_code_snippet path="app/Services/SabreAvailabilityService.php" mode="EXCERPT">
```php
/**
 * 批量查询多个酒店的最佳价格
 */
public function checkMultipleHotelsBestPrices(array $params): array
{
    // 实现批量查询逻辑
}

/**
 * 从查询结果中提取最佳价格信息
 */
protected function extractBestPriceFromResult($result, int $hotelId): ?array
{
    // 实现价格提取逻辑
}
```
</augment_code_snippet>

### 2. SabreController 新增接口

<augment_code_snippet path="app/Http/Controllers/V1/SabreController.php" mode="EXCERPT">
```php
/**
 * 批量查询多个酒店的最佳价格
 */
public function checkMultipleHotelsBestPrices(Request $request): JsonResponse
{
    // 实现控制器逻辑
}
```
</augment_code_snippet>

### 3. 新增路由

<augment_code_snippet path="routes/v1.php" mode="EXCERPT">
```php
Route::prefix('sabre')->group(function () {
    Route::post('/availability', [SabreController::class, 'checkAvailability']);
    Route::post('/multipleHotelsBestPrices', [SabreController::class, 'checkMultipleHotelsBestPrices']);
    // ... 其他路由
});
```
</augment_code_snippet>

## API 使用方法

### 接口地址
```
POST /api/v1/sabre/multipleHotelsBestPrices
```

### 请求示例
```json
{
    "hotelIds": [100823, 100824, 100825],
    "startDate": "2025-07-09",
    "endDate": "2025-07-11",
    "numRooms": 1,
    "adults": 2,
    "children": 1,
    "childrenAge": "8",
    "accessCode": "SUMMER2025"
}
```

### 响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "success": true,
        "hotels": [
            {
                "hotelId": 100823,
                "bestPrice": {
                    "currency": "USD",
                    "originalPrice": 25000,
                    "cnyPrice": 180000,
                    "priceDisplay": "¥1,800.00",
                    "rateCode": "BAR",
                    "rateName": "Best Available Rate",
                    "isMemberRate": false,
                    "guaranteePolicy": "GCC_CRD",
                    "cancelRuleString": "可免费取消至入住前24小时"
                },
                "room": {
                    "roomCode": "STD",
                    "roomName": "标准房",
                    "roomDescription": "舒适的标准客房"
                },
                "availableRoomsCount": 3,
                "totalRatesCount": 5
            }
        ],
        "total_count": 1,
        "errors": [],
        "query_params": {
            "startDate": "2025-07-09",
            "endDate": "2025-07-11",
            "numRooms": 1,
            "adults": 2,
            "children": 1,
            "accessCode": "***"
        }
    }
}
```

## 使用场景

1. **酒店列表页面**: 显示多个酒店的起始价格
2. **搜索结果页面**: 快速获取搜索结果中所有酒店的价格
3. **比价功能**: 帮助用户比较不同酒店的价格
4. **推荐系统**: 根据价格推荐性价比高的酒店
5. **价格监控**: 监控多个酒店的价格变化

## 性能优势

- **并发查询**: 同时查询多个酒店，减少总响应时间
- **缓存机制**: 相同参数的查询会使用缓存结果
- **部分成功**: 即使部分酒店查询失败也会返回成功的结果
- **最佳价格**: 自动筛选出每个酒店的最低价格
- **货币转换**: 自动转换为人民币显示

## 配置说明

在 `config/sabre_batch_query.php` 中可以配置：

- 批量查询限制（最大酒店数量、超时时间等）
- 缓存配置（缓存时间、缓存键等）
- 并发控制（并发数、批次大小等）
- 错误处理（重试次数、失败阈值等）
- 日志配置（日志级别、统计信息等）

## 测试

运行功能测试：
```bash
php artisan test tests/Feature/MultipleHotelsBestPricesTest.php
```

## 注意事项

1. **请求限制**: 一次最多查询20个酒店
2. **参数验证**: 严格的参数验证，确保数据有效性
3. **错误处理**: 支持部分成功，单个酒店失败不影响整体结果
4. **缓存策略**: 合理使用缓存，提高响应速度
5. **日志记录**: 详细的日志记录，便于问题排查

## 后续优化建议

1. **异步处理**: 考虑使用队列进行异步批量查询
2. **智能缓存**: 根据查询频率动态调整缓存策略
3. **负载均衡**: 在高并发场景下考虑负载均衡
4. **数据预热**: 对热门酒店进行数据预热
5. **监控告警**: 添加性能监控和告警机制
