.booking-page-wrap {
  .hotel-top {
    @apply pt-12 pb-14 relative;
    .logo {
      @apply w-20 h-20 rounded-full bg-white overflow-hidden flex items-center justify-center;
      img {
        @apply max-w-[80%] max-h-[80%];
      }
    }
    h1 {
      @apply font-30 font-medium text-white py-2 ml-5;
    }

    .back {
      @apply mt-6 text-white font-14;
      a {
        @apply flex flex-row items-center;
      }
    }

    .edit-tip {
      @apply flex flex-row items-center justify-between text-white bg-[#8bbcd9]/80 border border-[#EBEBEB]/40 px-4 py-3 font-14 rounded-lg;
      @apply mt-6;
    }
  }

  .select-room-content {
    .home-filter-wrap {
      @apply -mt-[39px];
      .filter-item {
        &.keyword { @apply hidden; }
      }
    }

    
  }

  .booking-step-wrap {
    @apply flex flex-row md:w-[560px] overflow-hidden mx-auto;
    .step-item {
      @apply flex-1 relative overflow-hidden flex flex-col items-center font-14;
      .icon {
        @apply w-[60px] h-[60px] rounded-full border border-black flex items-center justify-center text-black relative;
        i {
          @apply text-4xl;
        }
      }
      h4 {
        @apply py-1 mt-1;
      }
      &.active {
        @apply text-primary;
        .icon {
          @apply border-primary bg-primary text-white shadow-md shadow-[#300b5c]/30;
        }
      }
    }
    .dot-line {
      @apply relative;
      &::after, &::before {
        @apply hidden md:block content-none w-[999999px] h-px bg-transparent border-b border-black absolute top-1/2 left-16 -translate-y-1/2;
      }
      &::before {
        @apply right-16 left-auto;
      }
      &.left {
        &::before {
          @apply content-[''];
        }
      }
      &.right {
        &::after {
          @apply content-[''];
        }
      }
    }
  }

  .booking-login-wrap {
    @apply bg-no-repeat bg-center bg-cover rounded-lg shadow-lg shadow-[#265184]/10 p-6 text-white;
    background-image: url(../images/booking/booking-login-bg.png);
  }

  .book-detail-wrap {
    @apply border border-[#ebebeb] rounded-lg shadow-lg shadow-black/5 p-6 mt-4;
  }

  .order-room-detail-wrap {
    @apply border border-[#ebebeb] shadow-lg shadow-black/5 rounded-xl p-6;
  }
  .price-detail-wrap {
    @apply border border-[#320e5e] shadow-lg shadow-black/5 rounded-xl p-6 mt-4;
  }
}

@mixin hotel-room-item-swiper-el() {
  .swiper-el {
    @apply w-full md:w-[320px] pb-[64%] relative rounded-lg overflow-hidden;
    .nums {
      @apply bg-black/70 text-white rounded-full absolute bottom-2 right-2 px-2 py-0.5 z-10 font-12;
    }
    .swiper-pagination {
      @apply absolute bottom-2 z-10;
      .swiper-pagination-bullet {
        @apply bg-white opacity-100;
        &.swiper-pagination-bullet-active {
          @apply bg-primary;
        }
      }
    }
  }
}

.hotel-room-item {
  @apply border border-[#f5f5f5] rounded-lg px-4 py-4 md:px-6 md:py-10 flex flex-col md:flex-row md:justify-stretch mt-8 first:mt-0;
  .left {
    @apply md:pr-4 md:border-r border-[#f5f5f5];
    @include hotel-room-item-swiper-el();
  }
  .right {
    @apply flex-1 mt-4 md:mt-0;
    .room-title {
      @apply border-b border-[#f5f5f5] md:pl-6 pb-6;
      h2 {
        @apply font-20 font-semibold;
      }
      p {
        @apply font-14 mt-1.5;
      }
    }
    .price-list {
      // &.less {
      //   .price-item:nth-child(n+3) {
      //     @apply hidden;
      //   }
      // }
    }
    .price-item {
      @apply border-b border-[#f5f5f5] md:pl-6 pb-6 pt-6 last:border-b-0 last:pb-0;
      @apply flex flex-col md:flex-row justify-stretch;
      .desc-info {
        @apply flex flex-col flex-1 mr-8;
        h4 {
          @apply font-16 font-medium;
        }
        h5 {
          @apply font-14 text-primary mt-1;
        }
        p {
          @apply font-13 text-[#999];
        }
      }
      .price-info {
        @apply flex flex-row items-end md:flex-col mt-4 md:mt-0 md:w-[200px] md:text-right;
        h4 {
          @apply font-20;
          span {
            @apply font-16;
          }
        }
        h5 {
          @apply font-14;
        }
        p {
          @apply font-14 mt-1;
        }
      }
    }
    .toggle-more {
      @apply pt-4 flex flex-row justify-end border-t border-[#f5f5f5] mt-6 font-14 text-primary;
      i {
        @apply ml-1;
      }
    }
  }
}

.hotel-room-info-modal {
  @include hotel-room-item-swiper-el();
  .swiper-el {
    @apply w-full relative pb-[50%] rounded-b-none;
  }
}