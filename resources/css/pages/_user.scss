@use "../_utils/mixins.scss" as mixins;

.user-page-wrap {
  @apply bg-[#f9f9f9] lg:bg-white;
  .user-content-wrap {
    @apply flex-1 bg-[#f9f9f9] lg:bg-white;
    @apply lg:rounded-xl lg:border lg:border-[#919191]/20 overflow-hidden;
  }
  .user-dashboard-wrap {
    .ant-tabs {
      @apply rounded-xl overflow-hidden;
    }
    &.hide-ant-tabs-nav .ant-tabs-nav {
      // @apply hidden lg:flex;
      @apply hidden;
    }
    .ant-tabs-content-holder {
      @apply bg-white;
    }
    .ant-tabs-nav-wrap {
      @apply px-10 shadow-[0px_5px_5px_0px_rgba(0,0,0,0.03)];
      
    }
    .ant-tabs-top >.ant-tabs-nav::before {
      border-color: rgba(#919191, 0.2);
    }
    .ant-tabs-top >.ant-tabs-nav .ant-tabs-ink-bar {
      height: 3px;
    }

    .level-step-container {
      @apply pt-10 relative lg:h-44 mt-5;
      .line-track {
        @apply w-full h-1 bg-[#919191]/10 relative rounded-full overflow-hidden;
        span {
          @apply absolute top-0 left-0 h-full bg-[#919191]/40 rounded-full;
        }
      }
      .level-list {
        @apply absolute top-10 left-[10%] right-[10%] flex flex-row justify-between items-center;
        .level-item {
          @apply relative;
          .level-card { 
            @apply lg:w-[160px] lg:h-[100px] absolute top-0 translate-y-8 left-1/2 -translate-x-1/2 hidden bg-no-repeat bg-cover bg-center;
            img {
              @apply w-full h-full object-cover;
            }
          }
          .tracker {
            @apply rounded-full bg-white border border-[#919191]/20 inline-block p-1.5 -translate-y-1/2 cursor-pointer;
            span {
              @apply w-3 h-3 rounded-full bg-[#da9f59] block;
            }
          }
          h3 {
            @apply absolute -top-10 whitespace-nowrap text-[#737373];
            @include mixins.font-12;
          }
          @mixin level-item-unique($color, $card-image) {
            .tracker {
              span {
                background-color: $color;
              }
            }
            .level-card {
              // background-image: url($card-image);
            }
          }
          &:nth-child(1) {
            @include level-item-unique(#8BBCD9, "../images/user/level-card.png");
          }
          &:nth-child(2) {
            @include level-item-unique(#da9f59, "../images/user/level-card.png");
          }
          &:nth-child(3) {
            @include level-item-unique(#757C83, "../images/user/level-card.png");
          }
          &:nth-child(4) {
            @include level-item-unique(#141432, "../images/user/level-card.png");
          }
        }
      }
      .level-card-m {
        @apply mt-10 relative md:w-96 md:mx-auto lg:hidden;
        &::before {
          @apply content-[''] block w-full pb-[56.25%];
        }
        img {
          @apply w-full h-full absolute top-0 left-0 hidden;
        }
      }
      @mixin level-item-active {
        h3 {
          @apply font-semibold text-black -top-12;
          @include mixins.font-14;
        }
        .tracker {
          @apply border-4 border-[#919191]/50;
        }
        .level-card {
          @apply lg:block;
        }
      }
      &.active-1 {
        .line-track span {
          @apply w-[11%];
        }
        .level-list .level-item:nth-child(1) {
          @include level-item-active;
        }
        .level-card-m img:nth-child(1) {
          @apply block;
        }
      }
      &.active-2 {
        .line-track span {
          @apply w-[39%];
        }
        .level-list .level-item:nth-child(2) {
          @include level-item-active;
        }
        .level-card-m img:nth-child(2) {
          @apply block;
        }
      }
      &.active-3 {
        .line-track span {
          @apply w-[63%];
        }
        .level-list .level-item:nth-child(3) {
          @include level-item-active;
        }
        .level-card-m img:nth-child(3) {
          @apply block;
        }
      }
      &.active-4 {
        .line-track span {
          @apply w-[88%];
        }
        .level-list .level-item:nth-child(4) {
          @include level-item-active;
        }
        .level-card-m img:nth-child(4) {
          @apply block;
        }
      }
    }
    .right-icon-shadow {
      box-shadow: 0px 2px 4px 2px rgba(#000, .1);
    }
    
    // box-shadow: 0px 2px 4px 0px rgba(#000, .08);

  }

  .donate-banner {
    @apply pb-7.5 lg:pb-0 lg:min-h-[350px] bg-no-repeat bg-cover px-5 pt-7.5 bg-center;
    background-image: url(../images/user/donate-banner.png);
  }

  .donate-table {
    // &.ant-table-wrapper .ant-table-thead >tr>th {
    //   background-color: transparent;
    // }
    // .ant-table-cell::before {
    //   content: none !important;
    // }
  }

  .brands-wrap {
    @apply bg-white mt-4 pt-8;
  }
}

.hotel-image-mask {
  @apply absolute top-0 left-0 right-0 bottom-0;
  background-image: linear-gradient(rgba(35, 31, 32, 0.7) 0.16%, rgba(35, 31, 32, 0) 47.12%);
}

@mixin hotel-cover() {
  .cover-wrap {
    // background-image: url(https://storage.ghadiscovery.com/cdn-cgi/image/width=658,height=428,f=auto,g=auto,fit=cover/img/images/3/3/0/4/1444033-1-eng-GB/691b37af7221-Plume.jpg);
    @apply w-60 h-44 mr-5 overflow-hidden rounded-xl relative bg-no-repeat bg-center bg-cover;
    .logo-wrap {
      @apply bg-gradient-to-t from-transparent to-black/30 px-2.5 pt-2.5 absolute z-10 left-0 right-0;
      img {
        @apply filter invert max-w-24 max-h-24;
      }
    }
    .cover-swiper {
      @apply absolute top-0 left-0 right-0 bottom-0 z-[9];
      &:hover {
        .gha-swiper-button {
          @apply flex;
        }
      }
      .gha-swiper-button {
        @apply top-1/2 -translate-y-1/2 scale-75 md:hidden;
      }
      .gha-swiper-button-prev {
        @apply left-2.5;
      }
      .gha-swiper-button-next {
        @apply right-2.5;
      }
      .swiper-pagination-nums {
        @apply absolute bottom-1 right-2 bg-black/70 text-white rounded-full px-2 py-0.5 font-12 z-10;
      }
      .swiper-pagination {
        @apply bottom-1 !important;
        .swiper-pagination-bullet {
          @apply bg-white opacity-100;
          &.swiper-pagination-bullet-active {
            @apply bg-primary;
          }
        }
      }
      .swiper-container {
        @apply w-full h-full;
      }
    }
    .tag-wrap {
      @apply absolute bottom-0 left-0 right-0 bg-primary text-white text-center py-[5px] font-12 z-[2];
    }
    img.bg {
      @apply absolute inset-0 object-cover w-full h-full;
    }
  }
}

.order-item-wrap {
  @apply rounded-2xl border border-[#919191]/20 shadow-[0px_2px_4px_0px_rgba(0,0,0,0.04)] mt-5 first:mt-0 bg-white;
  .order-header {
    @apply pt-5 flex flex-row items-center font-12 px-5 pb-2.5 border-b border-[#919191]/20;
    .num {
      @apply flex-1;
    }
    .date {
      @apply text-[#919191];
    }
  }
  .order-content {
    @apply p-5 flex flex-col md:flex-row;
    @include hotel-cover;
    .cover-wrap {
      @apply w-full h-auto pb-[65%] md:h-44 md:pb-0 md:w-60 mr-0 md:mr-5;
    }
    .info-wrap {
      @apply flex-1 mt-4 lg:mt-0;
    }
  }
}

.hotel-item {
  @apply shadow-[0px_2px_4px_0px_rgba(0,0,0,0.08)] rounded-lg h-full flex flex-col;
  @include hotel-cover;
  .fav-icon {
    @apply absolute top-2.5 right-2.5 p-2 text-primary cursor-pointer z-[11];
    i {
      @apply text-4xl;
    }
  }
  .cover-wrap {
    @apply h-auto pb-[65%];
    // .bg {
    //   @apply aspect-[658/428];
    // }
  }
  &.hotel-item-large {
    // .cover-wrap {
    //   @apply h-56;
    // }
    .info-wrap {
      @apply p-4;
      .brand {
        @apply pb-2;
        
      }
      .local {
        @apply pb-1.5 mt-1;
      }
      .spt-line {
        @apply my-2;
      }
    }
  }
  &.recommend-hotel-item {
    @apply flex flex-col md:flex-row justify-stretch overflow-hidden border border-[#f5f5f5];
    .cover-wrap {
      @apply h-56 md:flex-1 md:h-auto rounded-none mr-2;
    }
    .info-wrap {
      @apply flex-1 flex flex-col md:flex-row md:p-6;
      .info-left {
        @apply flex-1;
      }
      .price-wrap {
        @apply md:mt-20;
      }
      .action-wrap {
        @apply flex md:flex-col justify-end md:ml-6;
        > a {
          @apply md:flex-none;
          &:nth-child(2) {
            @apply md:mt-3;
          }
        }
      }
    }
  }
  .cover-wrap {
    @apply w-full rounded-b-none;
  }
  .tag-wrap {
    @apply bg-primary text-white text-center py-[5px] font-12;
  }
  .info-wrap {
    @apply p-2.5 flex-1 flex flex-col text-black;
    .brand {
      @apply font-12 text-[#919191] pb-1.5;
    }
    .title {
      @apply font-16 font-medium;
    }
    .local {
      @apply font-12 mt-[3px] pb-1;
    }
    .room-area {
      @apply font-12 mt-[3px] pb-1;
    }
    .spt-line {
      @apply w-8 h-0.5 bg-[#919191] my-1.5;
    }
    .discount-line {
      @apply font-12 mt-[3px] pb-1 flex flex-row items-start;
      i {
        @apply text-primary mr-1.5 text-[24px];
      }
    }
    .price-wrap {
      @apply flex flex-row justify-between font-12 leading-none mt-3;
      .protrude {
        @apply text-[#DA9F59];
      }
      .price-num {
        @apply font-IvyMode-Reg font-18;
      }
    }
    .action-wrap {
      @apply flex flex-row py-[5px] -mx-[3px] mt-1.5;
      > a {
        @apply mx-[3px] flex-1;
      }
    }
  }
  &.hotel-discount-item {

  }
}

.hotel-activity-item {
  @apply h-[460px] rounded-lg bg-center bg-cover bg-no-repeat relative overflow-hidden;
  .info {
    @apply bottom-0 left-0 right-0 p-4 absolute text-white bg-gradient-to-t from-black/30 to-transparent;
    h1 {
      @apply font-18;
    }
    .spt-line {
      @apply w-8 h-0.5 bg-white my-1.5;
    }
    .discount-line {
      @apply font-12 mt-[3px] pb-1 flex flex-row items-start;
      i {
        @apply mr-1.5 text-[24px];
      }
    }
  }
  img.bg {
    @apply absolute inset-0 w-full h-full object-cover;
  }
}

.logout-modal {
  .ant-modal-content {
    @apply rounded-2xl py-8;
  }
  .ant-modal .ant-modal-close {
    @apply text-black;
  }
}

.user-nav-m-wrap {
  @apply border-b-2 border-[#919191]/20 overflow-x-auto fixed left-0 right-0 top-[90px] bg-white shadow-sm z-10;
  ul {
    @apply flex flex-row items-center -mx-2.5 whitespace-nowrap;
    li {
      @apply px-2.5;
      a {
        @apply font-14 !leading-[50px];
      }
      &.active {
        a {
          @apply text-primary border-b-[3px] border-primary;
        }
      }
    }
  }
}