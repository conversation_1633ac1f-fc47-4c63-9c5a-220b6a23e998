@use "../_utils/mixins.scss" as mixins;

.auth-page-wrap {
  .auth-box-wrap {
    background: linear-gradient(180deg, #0A1639 0%, #4A5193 35%, #D7AEC6 82.21%, #FFD2BA 100%);
    @apply py-24;
    .auth-box {
      @apply mx-4 p-8 bg-white rounded-xl flex flex-col;
      @apply md:flex-row md:w-[400px] md:mx-auto md:px-16 md:py-14;
      &.auth-box-500 {
        @apply md:w-[500px];
      }
      &.auth-box-300 {
        @apply md:w-[300px];
      }
      &.login-box {
        @apply md:w-[760px];
        .form-wrap {
          @apply md:pr-16;
          &::after {
            @apply content-none md:content-[""] w-px bg-black/10 absolute top-0 bottom-0 right-0;
          }
        }
      }
      .form-wrap {
        @apply relative flex-1;
        h3 {
          @apply text-lg font-bold mb-5;
        }
        .ant-btn {
          @apply shadow-none;
        }
        .ant-input, .ant-input-password {
          @apply px-0;
        }
      }
      .extra-wrap {
        @apply mt-12 flex-1 flex flex-col justify-center items-center relative;
        @apply md:mt-0 md:pl-16;
        h3 {
          @apply text-lg font-bold mb-5;
        }
        p {
          @apply text-xs text-center mb-5 text-[#300B5C] pb-12;
        }
        a {
          @apply border border-[#300B5C] text-[#300B5C] rounded-full w-full text-center py-1.5 text-sm;
        }
      }
    }
  }
}