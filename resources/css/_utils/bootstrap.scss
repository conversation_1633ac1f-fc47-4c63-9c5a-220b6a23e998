@use "mixins.scss" as mixins;

.top-linear-gradient {
  background: linear-gradient(180deg, #0A1639 0%, #4A5193 35%, #D7AEC6 82.21%, #FFD2BA 100%);
}

.driver-919191-20 {
  &::before, &::after {
    @apply border-[#919191]/20;
  }
}

@for $i from 12 through 42 {
  .font-#{$i} {
    @include mixins.font-size(#{$i}px);
  }
}

.gha-antd-modal {
  .ant-modal-content {
    @apply p-0;
  }
  .gha-antd-modal-wrapper {
    @apply relative bg-white rounded-2xl;
    .close-icon {
      @apply absolute top-2 right-2 cursor-pointer ;
      i {
        @apply text-3xl;
      }
    }
    .gha-antd-modal-content {
      @apply max-h-[80vh] overflow-x-hidden overflow-y-auto py-8;
    }
  }
}