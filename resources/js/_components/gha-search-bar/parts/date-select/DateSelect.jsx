import { ConfigProvider, DatePicker } from 'antd'
import useGhaSearchBarStore from "@/_components/gha-search-bar/useGhaSearchBarStore"
import locale from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
dayjs.locale('zh-cn');
const { RangePicker } = DatePicker;

export default function DateSelect({ }) {
  const date = useGhaSearchBarStore((state) => state.date);
  return (
    <div className="date-select search-item flex flex-row items-center">
      <i className="iconfont icon-Calendar"></i>
      <ConfigProvider 
        locale={locale}
        theme={{
          token: {
            colorPrimary: "#300B5C"
          },
          components: {
            DatePicker: {
              zIndexPopup: 2001
            },
          },
        }}
      >
          <RangePicker 
            variant="borderless" 
            placement="bottomLeft" 
            placeholder={"选择旅行时间"}
            suffixIcon={null}
            allowClear={false}
            onChange={(value) => useGhaSearchBarStore.setState({ date: value })}
            classNames={{popup: {root: "gha-popover-root !z-[2000]"}}}
            value={date}
            disabledDate={current => current && current < dayjs().startOf('day')}
          />
      </ConfigProvider>
    </div>
  )
}