import { useMemo } from "react";
import { Popover } from "antd";
import PeopleSelectPopover from "./PeopleSelectPopover";
import useGhaSearchBarStore from "@/_components/gha-search-bar/useGhaSearchBarStore"

export function formatPeople(rooms) {
    const adults = rooms.reduce((acc, cur) => acc + cur.adult, 0);
    const children = rooms.reduce((acc, cur) => acc + cur.child, 0);
    return `${adults}成人 ${children > 0 ? `${children}儿童` : ""} ${rooms.length}房间`;
}

export default function PeopleSelect() {
    const rooms = useGhaSearchBarStore(state => state.rooms);
    const formattedPeople = useMemo(() => {
        return formatPeople(rooms);
    }, [rooms]);
    function onRoomChange(roomIdx, type, val, ageIdx) {
        const updateRooms = [...rooms];
        if (type === "adult") {
            updateRooms[roomIdx].adult = val;
        }
        if (type === "child") {
            if (val > updateRooms[roomIdx].child) {
                updateRooms[roomIdx].childAges.push(0);
            } else {
                updateRooms[roomIdx].childAges.pop();
            }
            updateRooms[roomIdx].child = val;
        }
        if (type === "age") {
            updateRooms[roomIdx].childAges[ageIdx] = val;
        }
        // updateRooms[roomIdx]
        console.error(roomIdx, type, val, ageIdx);
        useGhaSearchBarStore.setState({ rooms: updateRooms });
    }
    function onRoomAdd() {
        const updateRooms = [...rooms, { adult: 1, child: 0, childAges: [] }];
        useGhaSearchBarStore.setState({ rooms: updateRooms });
    }
    function onRoomRemove(roomIdx) {
        const updateRooms = [...rooms];
        updateRooms.splice(roomIdx, 1);
        useGhaSearchBarStore.setState({ rooms: updateRooms });
    }
    return (
        <Popover
            content={
                <PeopleSelectPopover
                    rooms={rooms}
                    onRoomAdd={onRoomAdd}
                    onRoomRemove={onRoomRemove}
                    onRoomChange={onRoomChange}
                />
            }
            trigger="click"
            arrow={false}
            placement="bottomLeft"
            rootClassName="people-select-popover-root gha-popover-root"
            zIndex={2000}
        >
            <div className="people-select search-item flex flex-row items-center">
                <i className="iconfont icon-Family"></i>
                <a href="javascript:;">{formattedPeople}</a>
            </div>
        </Popover>
    );
}
