import { useEffect, useState, useRef, useMemo } from "react"
import { Input, Popover } from "antd"
import { $http } from "@/_utils/_index"
import useGhaSearchBarStore from "@/_components/gha-search-bar/useGhaSearchBarStore"
const { debounceTime, Subject, switchMap, map, catchError, of } = window.rxjs

export default function KeywordInput() {
  const keyword = useGhaSearchBarStore(state => state.keyword)
  const [focused, setFocused] = useState(false)
  const wrapperEl = useRef(null)
  const [width, setWidth] = useState(0)
  const inputSubject = useRef(new Subject()).current
  const [obscures, setObscures] = useState([])
  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        const width = entry.contentRect.width;
        setWidth(width)
      }
    });
    resizeObserver.observe(wrapperEl.current);
  }, [])
  useEffect(() => {
    const subscription = inputSubject.pipe(
      debounceTime(300), // 防抖时间设为 300ms
      switchMap(value => {
        if (!value.trim()) return of({success: true, data: []}); // 空值处理（可选）
        return $http.searchObscure(value).pipe(
          map(res => {
            const labelSet = {
              "country": "国家",
              "city": "城市",
              "hotel": "酒店",
              "brand": "品牌",
            }
            let newObscures = Object.keys(res.data || {}).map(key => {
              return {
                key, list: res.data[key], label: labelSet[key]
              }
            })
            return {success: true, data: newObscures.filter(e => e.list.length > 0)};
          }),
          catchError(err => {
            console.error("搜索错误:", err);
            return of({success: false, data: []}); // 错误处理
          })
        );
      })
    ).subscribe(res => {
      console.error("搜索结果:", res);
      if (res.success) {
        setObscures(res.data);
      }
    })
    return () => {
      subscription.unsubscribe(); // 清理订阅
      inputSubject.complete(); // 完成 Subject
    }
  }, [])
  function onOpenChange(boo) {
    // if (boo === false) {
    //   setOpen(boo)
    // }
  }
  function onInputChange(event) {
    useGhaSearchBarStore.setState({keyword: event.target.value})
    inputSubject.next(event.target.value);
    if (!event.target.value) {
      setObscures([])
    }
  }

  const popoverOpen = useMemo(() => {
    let obscuresAll = obscures.reduce((acc, cur) => {
      return acc.concat(cur.list)
    }, [])
    return focused && obscuresAll.length > 0
  }, [focused, obscures])

  const popoverContent = (
    <div className="max-h-[60vh] overflow-x-hidden overflow-y-auto md:!min-w-[400px]" style={{width: `${width}px`}}>
      <div className="px-4 py-6">
        {obscures.map(group => {
          return (
            <div key={group.key} className="mt-2 first:mt-0">
              <h2 className="ml-1.5 font-14">{group.label}</h2>
              <div className="">
                {group.list.map(item => {
                  const itemName = item.country_name || item.city_name || item.hotel_name || item.name;
                  return (
                    <div onMouseDown={() => {
                      console.error("选择了:", itemName);
                      onInputChange({target: {value: itemName}});
                    }} key={item.id} className="flex flex-row items-center font-16 cursor-pointer mt-1">
                      <i className="iconfont icon-Location mr-2 text-2xl"></i>
                      {itemName}
                    </div>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
  return (
    <Popover 
      rootClassName="z-[1998] gha-popover-root gha-popover-root-no-padding"
      content={popoverContent} 
      open={popoverOpen} 
      placement="bottomLeft"
      onOpenChange={onOpenChange}
      arrow={false}
      autoAdjustOverflow={false}
    >
      <div ref={wrapperEl} className="keyword-input search-item flex flex-row items-center relative">
        <i className="iconfont icon-Location"></i>
        <Input value={keyword} onFocus={() => setFocused(true)} onBlur={() => setFocused(false)} onChange={onInputChange} variant="borderless" placeholder="品牌/酒店/国家/城市"/>
      </div>
    </Popover>
    
    
  )
}