import { Input } from "antd"
import useGhaSearchBarStore from "@/_components/gha-search-bar/useGhaSearchBarStore"

export default function DiscountCodeInput() {
  const { promoCode } = useGhaSearchBarStore(state => state.promoCode);
  return (
    <div className="discount-code-input search-item flex flex-row items-center">
      <i className="iconfont icon-Award"></i>
      <Input value={promoCode} onChange={e => useGhaSearchBarStore.setState({promoCode: e.target.value})} variant="borderless" placeholder="促销码"/>
    </div>
  )
}