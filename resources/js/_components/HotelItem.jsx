import { useMemo } from "react"

export default function HotelItem({ hotel, className, onFav }) {
  const local = useMemo(() => {
    return [hotel.country, hotel.city?.[0]?.name].filter(e => e).join("，")
  }, [hotel])
  const logoUri = hotel.brand_img || ""
  return (
    <div className={`hotel-item ${className}`}>
      <div className="cover-wrap">
        <img className="bg" src={hotel.image}/>
        <div className="hotel-image-mask"></div>
        <div className="logo-wrap">
          {logoUri && <img src={logoUri} alt="" />}
        </div>
        <div onClick={(e) => {
          e.preventDefault()
          onFav()
        }} className="fav-icon"><i className={`iconfont ${hotel.is_collect ? 'icon-Heart-filled' : 'icon-Heart'} text-white`}></i></div>

      </div>
      <div className="tag-wrap" style={{backgroundColor: hotel.gradient_color}}>
        <p>{hotel.head_line || "Unknown Series"}</p>
      </div>
      <div className="info-wrap">
        <a href={`/about/brands/${hotel.brand_id}`} className="hover:underline">
          <div className="brand">{hotel.brand_name}</div>
        </a>

        <a href={`/hotel/${hotel.id}`} className="hover:underline">
          <h2 className="title">{hotel.hotel_name}</h2>
        </a>

        {local && <h5 className="local">{local}</h5>}
        <div className="spt-line"></div>
        <div className="flex-1"></div>
        <div className="price-wrap">
          <div className="protrude">
            <p>会员价低至</p>
            <p className="price-num">CNY1999</p>
          </div>
          <div className="">
            <p>会员价低至</p>
            <p className="price-num">CNY2999</p>
          </div>
        </div>
        <div className="action-wrap">
          <a href="" className="gha-primary-btn">立即预订</a>
          <a href={`/hotel/${hotel.id}`} className="gha-btn">酒店详情</a>
        </div>
      </div>
    </div>

  )
}
