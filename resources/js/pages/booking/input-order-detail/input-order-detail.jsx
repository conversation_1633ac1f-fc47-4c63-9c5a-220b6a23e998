import { createRoot } from "react-dom/client";
import { Form, Input, Button } from "antd"
import OrderStep from "@/pages/booking/select-room/parts/OrderStep";
import BookingLoginWidget from "@/pages/booking/widgets/BookingLoginWidget"
import OrderDetailWidget from "@/pages/booking/widgets/OrderDetailWidget";
import OrderPriceDetailWidget from "@/pages/booking/widgets/OrderPriceDetailWidget";
import GhaConfigProvider from "@/_components/GhaConfigProvider"
import CertCard01 from "@images/booking/cert-card-01.png"
import CertCard02 from "@images/booking/cert-card-02.png"
import CertCard03 from "@images/booking/cert-card-03.png"
import CertCard04 from "@images/booking/cert-card-04.png"
import CertCard05 from "@images/booking/cert-card-05.png"
import CertCard06 from "@images/booking/cert-card-06.png"

function App() {
  const [form] = Form.useForm();
  function onFinish(values) {
    console.log(values);
  }
  return (
    <>
      <div className="py-12">
        <OrderStep step={2}/>
      </div>
      <div className="">
        <div className="g-main-content-sm">
          <div className="text-[#999] font-14">
            <a href="javascript:;" className="flex flex-row items-center"><i className="iconfont icon-left font-20"></i>返回</a>
          </div>
          <div className="">
            <a href="javascript:;" className="underline text-primary font-14">已经是GHA探索之旅会员？</a>
          </div>
        </div>
      </div>
      <div className="mt-3">
        <div className="g-main-content-sm">
          <div className="flex flex-col-reverse lg:flex-row">
            <div className="flex-1">
              <div className="hidden lg:block">
                <BookingLoginWidget/>
              </div>
              
              <div className="book-detail-wrap">
                <div className="">
                  <Form
                    layout="vertical"
                    form={form}
                    onFinish={onFinish}
                    className="gha-form"
                  >
                    <h2 className="font-20 font-bold">您的入住信息</h2>
                    <div className="mt-4">
                      <h3 className="font-16 font-medium">入住联系人信息：</h3>
                      <p className="font-14 text-[#999] mt-1">请按实际入住人数填写，姓名与证件保持一致</p>
                    </div>
                    <div className="flex flex-col lg:flex-row mt-2">
                      <div className="flex-1">
                        <Form.Item label="姓" name="first_name" className='flex-1 lg:mr-5'>
                          <Input variant='underlined' placeholder="请输入您的姓" />
                        </Form.Item>
                      </div>
                      <div className="flex-1">
                        <Form.Item label="名" name="last_name" className='flex-1'>
                          <Input variant='underlined' placeholder="请输入您的名" />
                        </Form.Item>
                      </div>
                    </div>
                    <div className="flex flex-col lg:flex-row">
                      <div className="flex-1">
                        <Form.Item label="邮箱" name="first_name" className='flex-1 lg:mr-5'>
                          <Input variant='underlined' placeholder="请输入您的邮箱" />
                        </Form.Item>
                      </div>
                      <div className="flex-1">
                        <Form.Item label="手机号" name="last_name" className='flex-1'>
                          <Input variant='underlined' placeholder="请输入您的手机号" />
                        </Form.Item>
                      </div>
                    </div>
                    <div className="mt-4 mb-8 w-full h-px bg-[#c9c9c9]/50"></div>
                    <h3 className="font-16 font-medium">信用卡担保信息：</h3>
                    <p className="font-14 text-[#999] mt-1">仅需要信用卡详细信息来确认预订。 我们使用安全传输。</p>
                    <p className="font-14 mt-4">长沙玛珂酒店   支持以下信用卡</p>
                    <div className="flex flex-row flex-wrap -mx-1 mt-2">
                      {new Array(6).fill(0).map((_, index) => {
                        let cartSet = {
                          1: CertCard01,
                          2: CertCard02,
                          3: CertCard03,
                          4: CertCard04,
                          5: CertCard05,
                          6: CertCard06,
                        }
                        return (<img className="mx-1 w-10" key={index} src={cartSet[index + 1]} alt="" />)
                      })}
                    </div>
                    <div className="flex flex-row mt-4">
                      <div className="flex-1">
                        <Form.Item label="持卡人" name="first_name" className='flex-1 lg:mr-5'>
                          <Input variant='underlined' placeholder="请按照信用卡的姓名填写英文/拼音" />
                        </Form.Item>
                      </div>
                    </div>
                    <div className="flex flex-col lg:flex-row mt-2">
                      <Form.Item label="卡号" name="first_name" className='flex-1 lg:mr-5'>
                        <Input variant='underlined' placeholder="填写卡号" />
                      </Form.Item>
                      <Form.Item label="到期日" name="first_name" className='flex-1'>
                        <Input variant='underlined' placeholder="月 / 年" />
                      </Form.Item>
                    </div>
                    <div className="mt-4 mb-8 w-full h-px bg-[#c9c9c9]/50"></div>
                    <div className="font-13 text-primary">
                      <h3 className="font-16 font-medium text-black">取消及押金政策说明：</h3>
                      <p className="mt-2">取消房价不可取消，押金预订时需缴纳 1049.40 元押金，以保证您的预订。</p>
                      <p>*押金金额基于您选择的金额币种：人民币税费含税 税费 服务费</p>
                    </div>
                    <div className="my-8 w-full h-0.5 bg-[#c9c9c9]/30"></div>
                    <div className="flex flex-col lg:flex-row items-center justify-between pb-12">
                      <p className="text-primary font-14">
                        通过此次预订，您最多可赚取
                        <span className="text-[#e69d4a]">D$219</span>
                      </p>
                      <Button type="primary" className="gha-primary-btn mt-4 lg:mt-0 w-full lg:w-auto !px-10" htmlType="submit">下一步，确认订单</Button>
                    </div>
                  </Form>
                </div>
              </div>
            </div>
            <div className="lg:w-[320px] lg:ml-8">
              <div className="lg:hidden">
                <BookingLoginWidget/>
              </div>
              <OrderDetailWidget className="mt-4 lg:mt-0"/>
              <OrderPriceDetailWidget/>
            </div>
            
          </div>
        </div>
      </div>
    </>
  )
}

createRoot(document.querySelector(".input-order-detail-content")).render(<GhaConfigProvider><App /></GhaConfigProvider>);