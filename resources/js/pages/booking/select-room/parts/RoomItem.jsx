import React, { useState, useRef } from 'react'
import { Swiper, SwiperSlide } from "swiper/react"
import { Navigation, Pagination } from 'swiper/modules';
import { Modal } from "antd"

export default function RoomItem() {
  const [ showMorePrice, setShowMorePrice ] = useState(false)
  const componentId = useRef(Math.random().toString().slice(2, 10))
  const [ curSlide, setCurSlide ] = useState(1)
  const [ modalCurSlide, setModalCurSlide ] = useState(1)
  const [modalOpend, setModalOpend] = useState(false)
  return (
    <>
      <div className={`hotel-room-item componentId-${componentId.current}`}>
        <div className="left font-14">
          <div className="swiper-el">
            <div className="absolute top-0 left-0 right-0 bottom-0">
              <div className="swiper-pagination"></div>
              <div className="nums">{curSlide}/3</div>
              <Swiper 
                className='h-full'
                modules={[Pagination]}
                onSlideChange={e => setCurSlide(e.activeIndex + 1)}
                pagination={{el: `.componentId-${componentId.current} .swiper-pagination`, clickable: true}}
              >
                {new Array(3).fill(0).map((_, index) => {
                  return (
                    <SwiperSlide key={index}>
                      <img className='h-full' src="https://storage.ghadiscovery.com/cdn-cgi/image/height=1000,f=auto,fit=scale-down/img/images/0/8/9/3/1523980-1-eng-GB/8b9c17dd6311-ASIA-10-.jpg" alt="" />
                    </SwiperSlide>
                  )
                })}
              </Swiper>
            </div>
          </div>
          <div className="mt-3 hidden md:block">
            {new Array(3).fill(0).map(e => {
              return (
                <div className="flex flex-row items-center mt-1.5 first:mt-0">
                  <i className="iconfont icon-Programme_icons_celebration !text-lg mr-1 !leading-none"></i>
                  免费停车场
                </div>
              )
            })}
          </div>
          <div className="mt-1.5 hidden md:block">
            <a href="javascript:;" className="underline text-primary" onClick={() => setModalOpend(true)}>查看客房信息</a>
          </div>
        </div>
        <div className="right">
          <div className="room-title">
            <h2>M1 豪华客房（特大床）</h2>
            <p>1 张特大床，位于 5 至 6 楼。 客房配有一张写字台、两把扶手椅、一张咖啡桌以及带淋浴和浴缸的大理石浴室。 精选商品提供免费 Wi-Fi 和迷你酒吧。 最多入住人数： 3 （包括婴儿或儿童）。</p>
            <div className="text-right mt-2 md:hidden">
              <a href="javascript:;" className='underline text-primary font-14' onClick={() => setModalOpend(true)}>查看客房信息</a>
            </div>
          </div>
          <div className={`price-list`}>
            {new Array(3).fill(0).slice(0, showMorePrice ? 9999 : 2).map((_, index) => {
              return (
                <div className="price-item">
                  <div className="desc-info">
                    <h4>DISCOVERY 优惠房价</h4>
                    <h5>最高可获得$44奖励金</h5>
                    <div className="flex-1"></div>
                    <div className="mt-2">
                      <p>
                        此房价不可取消<br/>
                        预订时须支付 839.52 的押金，以担保预订。<br/>
                        *押金价值以您选择的货币为准：人民币 (CNY)
                      </p>
                    </div>
                  </div>
                  <div className="price-info">
                    <div className="">
                      <div className="text-[#e69d4a]">
                        <h5>会员价</h5>
                        <h4 className="font-IvyMode-Semi-Bd">CNY3,999<span>/晚</span></h4>
                      </div>
                      <p>包含税费及服务费</p>
                    </div>
                    
                    <div className="flex-1"></div>
                    <div className="mt-0 md:mt-8">
                      <a href="javascript:;" className="gha-primary-btn !py-1 w-full md:w-auto">立即预订</a>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
          <div className="toggle-more">
            <div onClick={() => setShowMorePrice(!showMorePrice)} className="cursor-pointer flex flex-row items-center"><span>{!showMorePrice ? "展开" : "收起"}</span><i className={`iconfont ${!showMorePrice ? "icon-zhankai" : "icon-shouqi"}`}></i></div>
          </div>
        </div>
      </div>
      <Modal 
        open={modalOpend}
        footer={null}
        width={700}
        closable={false}
        destroyOnHidden
        rootClassName='gha-antd-modal hotel-room-info-modal'
      >
        <div className='relative'>
          <div className="absolute flex items-center justify-center -top-6 -right-2 lg:top-0 lg:-right-20 w-12 h-12 bg-white rounded-full cursor-pointer z-20" onClick={() => setModalOpend(false)}>
            <i className='iconfont icon-Close font-24'></i>
          </div>
          <div className={`swiper-el`}>
            <div className="absolute top-0 left-0 right-0 bottom-0">
              <div className={`swiper-pagination`}></div>
              <div className="nums">{modalCurSlide}/3</div>
              <Swiper 
                className='h-full'
                modules={[Pagination]}
                onSlideChange={e => setModalCurSlide(e.activeIndex + 1)}
                pagination={true}
              >
                {new Array(3).fill(0).map((_, index) => {
                  return (
                    <SwiperSlide key={index}>
                      <img className='h-full w-full' src="https://storage.ghadiscovery.com/cdn-cgi/image/height=1000,f=auto,fit=scale-down/img/images/0/8/9/3/1523980-1-eng-GB/8b9c17dd6311-ASIA-10-.jpg" alt="" />
                    </SwiperSlide>
                  )
                })}
              </Swiper>
            </div>
          </div>
          <div className="px-4 py-6">
            <h2 className="font-20 font-semibold">M2尊贵大床房</h2>
            <div className="flex flex-row items-center mt-1">
              <i className="iconfont icon-mianji mr-2"></i> 40-50平方米
            </div>
            <div className="my-4 w-20 h-0.5 bg-black"></div>
            <p>这间空调客房配有平板卫星电视、保险箱和沏茶/咖啡设施。 提供免费 WiFi。这间空调客房配有平板卫星电视、保险箱和沏茶/咖啡设施。 提供免费 WiFi。</p>
            <div className="gha-divider my-3"><p className="px-4 font-bold">客房设施</p></div>
            <div className="flex flex-row flex-wrap -mx-4">
              {new Array(10).fill(0).map((_, index) => {
                return (
                  <div key={index} className="w-1/2">
                    <div className="px-4 flex flex-row items-center mt-1">
                      <i className='iconfont icon-Taxi font-20'></i>
                      <p className="ml-1">{`WIFI`}</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </Modal>
    </>
    
  )
}