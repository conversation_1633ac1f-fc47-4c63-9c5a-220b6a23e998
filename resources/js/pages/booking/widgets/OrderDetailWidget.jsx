import { useState } from "react"
import { Modal } from "antd"

export default function OrderDetailWidget({className, title, hasUserInfo, hasOrderTitle}) {
  const [policyModalOpend, setPolicyModalOpened] = useState(false)
  return (
    <>
      <div className={`order-room-detail-wrap ${className || ""}`}>
        <h2 className="font-20 font-semibold">{title || "您的预订"}</h2>
        
        {hasOrderTitle ? (
          <>
            <div className="pb-6 pt-2 font-14 border-b border-[#c9c9c9]/50">
              <p>订单状态：<span className="text-primary">已预约</span></p>
              <p className="mt-0.5">订单号：GHA12348990</p>
            </div>
            <div className="flex flex-row items-center border-b border-[#c9c9c9]/50 py-6">
              <div className="flex-1">
                <h4 className="font-16 mt-1">长沙玛拉顿酒店</h4>
                <div className="flex flex-row items-start font-14 mt-1">
                  <i className="iconfont icon-Location text-[#bbb] text-[22px]"></i>
                  中国湖南省长沙市芙蓉区蔡锷中路 1 号国金中心 T2 塔楼
                </div>
              </div>
              <div className="ml-2 w-24 h-14 md:w-36 md:h-20 rounded-lg border border-[#efefef] shadow-md flex items-center justify-center">
                <img className="max-w-[90%] max-h-[90%]" src="https://cms.ghadiscovery.com/content/download/518/2348?version=38&inline=1" alt="" />
              </div>
            </div>
          </>
          
        ) : (
          <div className="border-b border-[#c9c9c9]/50 py-6">
            <h3 className="font-18 mt-2">Maqo</h3>
            <h4 className="font-16 mt-1">长沙玛拉顿酒店</h4>
            <div className="flex flex-row items-start font-14 mt-1">
              <i className="iconfont icon-Location text-[#bbb] text-[22px]"></i>
              中国湖南省长沙市芙蓉区蔡锷中路 1 号国金中心 T2 塔楼
            </div>
          </div>
        )}
        <div className="py-6 border-b border-[#c9c9c9]/50">
          <div className="flex flex-row items-center justify-between">
            <h3 className="font-16 font-semibold">日期</h3>
            <i className="iconfont icon-edit cursor-pointer text-[20px]"></i>
          </div>
          <div className="font-14 mt-1">
            <p>2023-02-26至2023-02-28， 2晚</p>
            <p>成人 2， 儿童 1</p>
          </div>
        </div>
        <div className="pt-6">
          <div className="flex flex-row items-center justify-between">
            <h3 className="font-16 font-semibold">房间</h3>
            <i className="iconfont icon-edit cursor-pointer text-[20px]"></i>
          </div>
          <div className="">
            {new Array(3).fill(0).map((_, index) => {
              return (
                <div className="mt-3 first:mt-1">
                  <h4 className="font-15">房间一：</h4>
                  <p className="font-14 mt-1">M1 豪华客房（特大床）</p>
                  <a onClick={() => setPolicyModalOpened(true)} className="font-14 mt-1 text-primary underline" href="javascript:;" >取消及税收政策</a>
                </div>
              )
            })}
          </div>
        </div>
        {hasUserInfo && (
          <>
            <div className="mt-6 pt-6 border-t border-[#c9c9c9]/50">
              <h3 className="font-16 font-semibold">入住人信息</h3>
              <h4 className="font-16 mt-1 font-IvyMode-Reg">Bejnd 李</h4>
              <p className="font-14 mt-1">SILVER 会员号：8870636037</p>
              <p className="font-14 mt-1">邮箱：<EMAIL></p>
            </div>
          </>
          
        )}
      </div>
      <Modal 
        open={policyModalOpend}
        footer={null}
        width={700}
        closable={false}
        destroyOnHidden
        transitionName="ant-fade"
        rootClassName='gha-antd-modal'
      >
        <div className="gha-antd-modal-wrapper">
          <div className="close-icon" onClick={() => setPolicyModalOpened(false)}>
            <i className="iconfont icon-Close"></i>
          </div>
          <div className="gha-antd-modal-content">
            <div className="px-8">
              <h2 className="font-20 text-center mt-1 font-bold">取消及税收政策</h2>
              <div className="">
                {new Array(2).fill(0).map((_, idx) => {
                  return (
                    <div key={idx} className="py-6 border-b border-[#919191]/20 font-14 last:border-b-0">
                      <div className="">
                        <p className="font-13 mb-0.5">房间1</p>
                        <h4 className="font-18 font-IvyMode-Reg">M2尊贵客房（大床）</h4>
                      </div>
                      <div className="mt-2">
                        <p className="font-13 mb-0.5">房价</p>
                        <p>会员特惠价</p>
                      </div>
                      <div className="mt-2">
                        <p className="font-13 mb-0.5">取消</p>
                        <p>不迟于抵达前24小时取消，否则将收取一晚的取消费用</p>
                      </div>
                      <div className="mt-2">
                        <p className="font-13 mb-0.5">存款</p>
                        <p>每次预订都需要信用卡</p>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
            <div className="pt-6 border-t border-[#919191]/20">
                <div className="px-8">
                  <h4 className="font-bold font-16">税金</h4>
                  <p className="mt-1">10%服务费-953元人民币</p>
                  <p className="mt-1">增值税-153元人民币</p>
                </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  )
}