import { useState } from "react"
import { Modal } from "antd"

export default function OrderPriceDetail({className}) {
  const [modalOpend, setModalOpened] = useState(false)
  const [cancelModalOpend, setCancelModalOpened] = useState(true)
  return (
    <>
      <div className={`price-detail-wrap ${className || ""}`}>
        <div className="flex flex-row items-center justify-between font-16">
            <h3 className="font-16 font-semibold">价格详情</h3>
            <a className="underline font-14" href="javascript:;" onClick={() => setModalOpened(true)}>查看价格明细</a>
        </div>
        <div className="py-3 border-b border-[#c9c9c9]/20 mt-2">
          <div className="flex flex-row items-center justify-between font-14">
            <p>小计</p>
            <p>CNY 5,668.30</p>
          </div>
        </div>
        <div className="py-3 border-b border-[#c9c9c9]/20">
          <div className="flex flex-row items-center justify-between font-14">
            <p>小计</p>
            <p>CNY 5,668.30</p>
          </div>
        </div>
        <div className="pt-4">
          <div className="flex flex-row items-center justify-between font-14">
            <p className="font-medium">总计</p>
            <p className="font-20 font-IvyMode-Reg">CNY 6,668.30</p>
          </div>
        </div>
      </div>

      <Modal 
        open={modalOpend}
        footer={null}
        width={700}
        closable={false}
        destroyOnHidden
        transitionName="ant-fade"
        rootClassName='gha-antd-modal'
      >
        <div className="gha-antd-modal-wrapper">
          <div className="close-icon" onClick={() => setModalOpened(false)}>
            <i className="iconfont icon-Close"></i>
          </div>
          <div className="gha-antd-modal-content">
            <div className="px-8">
              <div className="gha-divider w-32 mx-auto"><p className="px-2 text-[#0c0509]/20">价格明细</p></div>
              <h2 className="font-20 text-center mt-1">长沙马赫酒店</h2>
              <div className="mt-6">
                {new Array(2).fill(0).map((_, idx) => {
                  return (
                    <div key={idx} className="p-6 border border-[#919191]/20 shadow-md rounded-lg mt-3 first:mt-0 font-14">
                      <h4 className="font-15">M2尊贵客房（大床）</h4>
                      <p className="mt-1">会员特价</p>
                      <div className="mt-1">
                        <div className="py-1.5 border-b border-[#919191]/20 flex flex-row items-center justify-between">
                          <p>2025-05-08</p>
                          <p className="font-IvyMode-Reg">CNY 2,834.15</p>
                        </div>
                        <div className="py-1.5 border-b border-[#919191]/20 flex flex-row items-center justify-between">
                          <p>2025-05-08</p>
                          <p className="font-IvyMode-Reg">CNY 2,834.15</p>
                        </div>
                      </div>
                      <div className="mt-1 py-1.5 flex flex-row items-center justify-between">
                        <p>房间1合计</p>
                        <p className="font-IvyMode-Reg font-16">CNY 2,834.15</p>
                      </div>
                    </div>
                  )
                })}
                
              </div>
            </div>
            <div className="mt-8 pt-2 border-t border-[#919191]/20">
                <div className="px-8">
                  <div className="py-1.5 border-b border-[#919191]/20 flex flex-row items-center justify-between">
                    <p>小计</p>
                    <p className="font-IvyMode-Reg">CNY 2,834.15</p>
                  </div>
                  <div className="py-1.5 border-b border-[#919191]/20 flex flex-row items-center justify-between">
                    <p>税费</p>
                    <p className="font-IvyMode-Reg">CNY 2,834.15</p>
                  </div>
                  <div className="py-1.5 flex flex-row items-center justify-between">
                    <p>总计</p>
                    <p className="font-IvyMode-Reg font-18">CNY 2,834.15</p>
                  </div>
                </div>
            </div>
          </div>
        </div>

      </Modal>
      
    </>
    
  )
}