import { createRoot } from "react-dom/client";
import { useState } from "react";
import { Button, Form, Input } from 'antd';
import GhaConfigProvider, { useAntdMessage } from "@/_components/GhaConfigProvider";
import { $http, $helper, $constants, $storage } from "@/_utils/_index"

function LoginForm() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false)
  const message = useAntdMessage()
  function onFinish(values) {
    setLoading(true)
      $http.authLogin(values).subscribe({
          next: (res) => {
              setLoading(false);
              if (res.status_code !== 200) {
                  message.error(res.message);
                  return;
              }
              if (res.data.token) {
                  $storage.setToken(res.data.token);
              }
              let redirect = new URLSearchParams(location.search.replace("?", "")).get("redirect") || "/";
              if (redirect) {
                  location.href = redirect;
              }
          },
          error: (err) => {
              console.error('登录请求错误:', err);
              setLoading(false);
              message.error('登录失败，请检查网络连接');
          },
          complete: () => {
              console.log('请求完成');
          }
      });
  }
  return (
    <div className="auth-box login-box">
      <div className="form-wrap">
        <h3>请登录</h3>
        <Form
          layout="vertical"
          form={form}
          requiredMark={false}
          initialValues={$helper.isLaravelLocal() ? {
            email: "<EMAIL>",
            password: "123456Abc$",
          } : {}}
          onFinish={onFinish}
        >
          <Form.Item label="邮箱*" name="email" 
            rules={[
              {required: true, message: "请输入您的邮箱"},
              { 
                pattern: $constants.emailPattern,
                message: "邮箱格式不正确"
              } 
            ]}
          >
            <Input variant='underlined' placeholder="请输入您的邮箱" />
          </Form.Item>
          <Form.Item label="密码*" name="password" rules={[{required: true, message: "请输入密码"}]}>
            <Input.Password variant='underlined' placeholder="请输入密码" />
          </Form.Item>
          <p className='text-right -mt-3.5 mb-5'><a href={window.__ServerVars__.forgetPwdUri} className='underline'>忘记密码?</a></p>
          <Form.Item label={null}>
            <Button className="gha-primary-btn" loading={loading} type="primary" shape="round" block htmlType="submit">登录</Button>
          </Form.Item>
          <p className='text-center'><a href={window.__ServerVars__.activeUri} className='underline'>激活您的在线帐户</a></p>
        </Form>
      </div>
      <div className="extra-wrap">
        <h3>免费加入GHA全球会员计划</h3>
        <p>
          GHA代表着40个品牌,<br/>
          800多家酒店,遍布100个国家,<br/>
          服务会员25万<br/>
        </p>
        <a className="gha-btn" href={window.__ServerVars__.registerUri}>立即加入GHA酒店忠诚计划</a>
      </div>
    </div>
  )
}

createRoot(document.querySelector(".auth-box-wrap")).render(<GhaConfigProvider><LoginForm /></GhaConfigProvider>);
