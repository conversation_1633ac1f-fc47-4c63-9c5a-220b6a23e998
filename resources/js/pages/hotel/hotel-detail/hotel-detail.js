
import MockHotelDataSource from '@/pages/hotel/hotel-list/parts/mock-hotel'
import { $ghaMapHelper, $helper } from "@/_utils/_index"

$(function() {
  new HotelDetailController()
})
class HotelDetailController {
  constructor() {
    this.bootstrap()
    this.mapBuilded = false
  }

  bootstrap() {
    this.initBrandDetailSwiper('.brands-hotel-block')
    this.initBrandDetailSwiper('.brands-discount-block')
    this.initBrandDetailSwiper('.hotel-activity-block')
    this.initFacilityMore()
    this.initThumbSwiper()
    this.initHotelRoomItemSwiper()
    this.initRecommendedHotelSwiper()
    this.initFixedNavBar()
    this.initNavScrollActive()
    this.initModalMap()
  }
  initModalThumbGlary() {
    $(".gha-modal-wrap.thumbs .swiper-slide img").each(function() {
      $(this).attr("src", $(this).attr("data-src"))
    })
    $(".glary-item").each(function(idx) {
      let mSwiper = new Swiper(`.thumbs-swiper-m.thumbs-swiper-${idx} .swiper-container`, {
        slidesPerView: "auto",
        spaceBetween: 10,
        navigation: {
          nextEl: `.thumbs-swiper-m.thumbs-swiper-${idx} .gha-swiper-button-next`,
          prevEl: `.thumbs-swiper-m.thumbs-swiper-${idx} .gha-swiper-button-prev`,
        },
        on: {
          slideChange: function() {
            $(`.thumbs-swiper-m.thumbs-swiper-${idx} .page-count span`).text(this.activeIndex + 1)
          }
        },
      })
      let mainSwiper = new Swiper(`.thumbs-swiper-top.thumbs-swiper-${idx} .swiper-container`, {
        navigation: {
          nextEl: `.thumbs-swiper-top.thumbs-swiper-${idx} .gha-swiper-button-next`,
          prevEl: `.thumbs-swiper-top.thumbs-swiper-${idx} .gha-swiper-button-prev`,
        },
        on: {
          slideChange: function() {
            $(`.thumbs-swiper-top.thumbs-swiper-${idx} .page-count span`).text(this.activeIndex + 1)
            thumbSwiper.slideTo(this.activeIndex)
            $(`.thumbs-swiper-bottom.thumbs-swiper-${idx} .swiper-slide`).removeClass("active")
            thumbSwiper.slides[this.activeIndex].classList.add("active")
          }
        },
      })
      let thumbSwiper = new Swiper(`.thumbs-swiper-bottom.thumbs-swiper-${idx} .swiper-container`, {
        slidesPerView: 'auto',
        spaceBetween: 8,
        freeMode: true,
        // watchSlidesProgress: true,
        slidesOffsetBefore: 32,
        slidesOffsetAfter: 32,
      })
      $(`body`).on("click", `.thumbs-swiper-bottom.thumbs-swiper-${idx} .swiper-container .swiper-slide`, function() {
        const index = $(this).index()
        mainSwiper.slideTo(index)
      })
    })
    $("body").on("click", ".glary-item", function() {
      $(".glary-item").removeClass("active")
      $(this).addClass("active")
      const idx = $(this).index()
      console.error("idx", idx)
      $(`.thumbs-swiper-m`).addClass("hidden")
      $(`.thumbs-swiper-top`).addClass("hidden")
      $(`.thumbs-swiper-bottom`).addClass("hidden")
      $(`.thumbs-swiper-m.thumbs-swiper-${idx}`).removeClass("hidden")
      $(`.thumbs-swiper-top.thumbs-swiper-${idx}`).removeClass("hidden")
      $(`.thumbs-swiper-bottom.thumbs-swiper-${idx}`).removeClass("hidden")
    })
  }
  initModalMap() {
    $helper.getGlobalSubject().on("updateItemFav", function(event) {
      window.$hotel.is_collect = event.data.nextState
      let $favEl = $(`.trigger-fav-el[data-type="${event.data.type}"][data-id="${event.data.id}"]`)
      $favEl.find("i").removeClass("icon-Heart").removeClass("icon-Heart-filled")
      $favEl.find("i").addClass(event.data.nextState ? "icon-Heart-filled" : "icon-Heart")
    })
    const that = this
    $("body").on("click", ".gha-modal-wrap .close", function() {
      $(".gha-modal-wrap").addClass("hidden")
      $("body").removeClass("overflow-hidden")
    })
    $("body").on("click", "#toggleMapBtn", function() {
      $(".gha-modal-wrap.map").removeClass("hidden")
      $("body").addClass("overflow-hidden")
      that.buildMap()
    })

    $("body").on("click", "#toggleThumbsBtn", function() {
      $(".gha-modal-wrap.thumbs").removeClass("hidden")
      $("body").addClass("overflow-hidden")
      that.initModalThumbGlary()
    })
  }

  buildMap() {
    if (this.mapBuilded) return
    this.mapBuilded = true
    let hotelData = window.$hotel
    mapboxgl.accessToken = 'pk.eyJ1IjoiaWlpc2xlZSIsImEiOiJjbHJoN2Z3djMwbjY0MmptampmODRlcWdvIn0.yak7m5pJUycZ58aJUst7ag'
    const map = new mapboxgl.Map({
      container: 'modalMap', // container ID
      style: "mapbox://styles/mapbox/streets-v12",
      zoom: 10,
      center: [hotelData.longitude, hotelData.latitude]
    });
    map.addControl(new MapboxLanguage({
        defaultLanguage: 'zh-Hans'
    }));
    map.on("load", function() {
      $(".gha-mapbox").removeClass("loading")
      $ghaMapHelper.fitBoundsMap(map, [[hotelData.longitude, hotelData.latitude]])
      let item = hotelData
      let markerEl
      markerEl = document.createElement('div')
      markerEl.className = 'gha-mapbox-marker-invalid'
      $(markerEl).append(`<i class="iconfont icon-Union"></i>`)
      
      const marker = new mapboxgl.Marker(markerEl)
        .setLngLat([item.longitude, item.latitude])
        .addTo(map);
      marker.getElement().addEventListener("click", function() {
        $ghaMapHelper.flyMarkerToCenter(map, [item.longitude, item.latitude])
        const popup = $ghaMapHelper.getPopup({hotelData, slug: "hotels", autoTriggerFav: true})
        marker.setPopup(popup)
        marker.togglePopup()
        $ghaMapHelper.initPopupSwiper(hotelData)
      })
    })
  }
  initFixedNavBar() {
    const selector = ".fixed-nav-bar"
    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        const height = entry.contentRect.height;
        if ($(selector).find(".fixed-holder")[0]) {
          $(selector).find(".fixed-holder")[0].style.height = `${height}px`;
        }
      }
    });
    resizeObserver.observe(document.querySelector(`${selector} .bar-el`));
    
    const sentinelTop = $(selector).find(`.fixed-sentinel-top`)[0];
    const target = $(selector)[0];
    if (!target || !sentinelTop) return;
    function handleScroll() {
      const isLg = $(window).width() >= 1024;
      if (sentinelTop.getBoundingClientRect().top < (isLg ? 168 : 146)) {
        target.classList.add('gha-fixed');
      } else {
        target.classList.remove('gha-fixed');
      }
    }
    window.addEventListener('scroll', handleScroll, true)
    handleScroll()
  }
  initNavScrollActive() {
    function setTabItemActive(idx) {
      if (!$(".brand-desc-tab-item").eq(idx).find("a").hasClass("active")) {
        $(".brand-desc-tab-item").find("a").removeClass("active")
        $(".brand-desc-tab-item").eq(idx).find("a").addClass("active")
      }
    }
    function handleScroll() {
      let anchorTopArrays = new Array($(".brand-desc-tab-item").length).fill(0).map((_, idx) => {
        return document.querySelector(`#anchor${idx}`).getBoundingClientRect().top
      })
      let topIdx = anchorTopArrays.findIndex(e => e > 10)
      if (topIdx === -1) {
        setTabItemActive($(".brand-desc-tab-item").length - 1)
        return
      }
      if ([0, 1].includes(topIdx)) {
        setTabItemActive(0)
        return
      }
      setTabItemActive(topIdx - 1)
    }
    window.addEventListener('scroll', handleScroll)
    handleScroll()
  }
  initRecommendedHotelSwiper() {
    new Swiper(`.recommend-swiper .swiper-container`, {
      slidesPerView: 1.1,
      spaceBetween: 24,
      breakpoints: {
        768: {
          slidesPerView: 1,
        },
      },
      navigation: {
        nextEl: `.recommend-swiper .gha-swiper-button-next`,
        prevEl: `.recommend-swiper .gha-swiper-button-prev`,
      },
      pagination: {
        el: `.recommend-swiper .swiper-pagination`,
        clickable: true,
      }
    })
  }

  initHotelRoomItemSwiper() {
    const itemSelector = ".hotel-room"
    $(itemSelector).each(function() {
      const id = $(this).data("id")
      new Swiper(`${itemSelector}[data-id='${id}'] .cover-swiper .swiper-container`, {
        navigation: {
          nextEl: `${itemSelector}[data-id='${id}'] .cover-swiper .gha-swiper-button-next`,
          prevEl: `${itemSelector}[data-id='${id}'] .cover-swiper .gha-swiper-button-prev`,
        },
        pagination: {
          el: `${itemSelector}[data-id='${id}'] .cover-swiper .swiper-pagination`,
          clickable: true,
        },
        on:{
          slideChange: function() {
            $(`${itemSelector}[data-id='${id}'] .cover-swiper .swiper-pagination-nums span.cur`).text(this.activeIndex + 1)
          },
        }
        
      })
    })
  }

  initFacilityMore() {
    $("body").on("click", ".facility-block .facility-toggle-btn", function() {
      $(".facility-block").toggleClass("show-more")
    })
  }

  initThumbSwiper() {
    new Swiper(`.thumb-swiper .swiper-container`, {
      navigation: {
        nextEl: `.thumb-swiper .gha-swiper-button-next`,
        prevEl: `.thumb-swiper .gha-swiper-button-prev`,
      },
      pagination: {
        el: `.thumb-swiper .swiper-pagination`,
        clickable: true,
      },
    })
  }

  initBrandDetailSwiper(blockSelector) {
    new Swiper(`${blockSelector} .gha-swiper >.swiper-container`, {
      slidesPerView: 1.1,
      spaceBetween: 24,
      slidesPerGroup: 1,
      breakpoints: {
        768: {
          slidesPerView: 2,
        },
        1024: {
          slidesPerView: 3,
        },
      },
      on:{
        slideChange: function() {
          updateTransparentSlides(this);
          // console.error("this.slides", this.slides, this.activeIndex)
          // alert('改变了，activeIndex为'+this.activeIndex);
        },
        init: function() {
          updateTransparentSlides(this);
        },
      },
      navigation: {
        nextEl: `${blockSelector} .gha-swiper >.gha-swiper-button-next`,
        prevEl: `${blockSelector} .gha-swiper >.gha-swiper-button-prev`,
      },
      pagination: {
        el: `${blockSelector} .gha-swiper >.swiper-pagination`,
        clickable: true,
      },
    })

    function updateTransparentSlides(swiperInstance) {
      $(`${blockSelector} .swiper-cur-idx`).text(swiperInstance.activeIndex + 1)
      let slidesPerView = 3
      if (window.innerWidth < 1024) {
        slidesPerView = 2
      }
      if (window.innerWidth < 768) {
        slidesPerView = 1
      }
      // 移除所有透明类
      swiperInstance.slides.forEach((slide, idx) => {
        slide.classList.remove('transparent-slide-30');
        slide.classList.remove('transparent-slide-0');
        if (idx > swiperInstance.activeIndex + slidesPerView) {
          slide.classList.add("transparent-slide-0")
        }
        if (idx === swiperInstance.activeIndex + slidesPerView) {
          slide.classList.add("transparent-slide-30")
        }
        if (idx < swiperInstance.activeIndex - 1) {
          slide.classList.add("transparent-slide-0")
        }
        if (idx === swiperInstance.activeIndex - 1) {
          slide.classList.add("transparent-slide-30")
        }
      });
      
      // 计算目标索引
      const targetIndex = swiperInstance.activeIndex + 3;
      
      // 如果目标索引存在，则添加透明类
      if (targetIndex < swiperInstance.slides.length) {
        swiperInstance.slides[targetIndex].classList.add('transparent-slide-30');
      }
    }
  }
}