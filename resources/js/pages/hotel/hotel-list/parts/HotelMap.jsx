import { useEffect, useRef, forwardRef, useImperative<PERSON>andle } from "react"
import dIcon from "@images/d-icon.png"
import HotelMapBar from "./HotelMapBar"
import useSearchStore from "@/pages/hotel/hotel-list/store/useSearchStore"
import { $ghaMapHelper } from "@/_utils/_index"

const HotelMap = forwardRef(({ dataSource, onHotelLike }, ref) => {
  const componentId = useRef(Math.random().toString(36).substr(2, 9)).current
  const dataSourceRef = useRef(dataSource)
  const slug = useSearchStore((state) => state.slug)
  const mapRef = useRef(null)
  useImperativeHandle(ref, () => ({
    updateMapData(data) {
      dataSourceRef.current = data
      initMap()
    }
  }));
  
  useEffect(() => {
    initMap()
  }, [])
  useEffect(() => {
    $("body").on("click", ".gha-mapbox-hotel-popup .fav-icon", function() {
      const $el = $(this)
      const id = $el.data("id")
      if ($el.hasClass("loading")) return
      $el.addClass("loading")
      const $makerFavEl = $(`.gha-mapbox-marker-${id} .fav-icon`)
      onHotelLike(id, function(nextState, _dataSource) {
        $el.removeClass("loading").removeClass('icon-Heart').removeClass('icon-Heart-filled')
        $el.addClass(nextState ? "icon-Heart-filled" : "icon-Heart")
        $makerFavEl.removeClass('icon-Heart').removeClass('icon-Heart-filled')
        $makerFavEl.addClass(nextState ? "icon-Heart-filled" : "icon-Heart")
        dataSourceRef.current = _dataSource
      })
    })
  }, [])

  function initMap() {
    $(".gha-mapbox").addClass("loading")
    mapboxgl.accessToken = 'pk.eyJ1IjoiaWlpc2xlZSIsImEiOiJjbHJoN2Z3djMwbjY0MmptampmODRlcWdvIn0.yak7m5pJUycZ58aJUst7ag'
    const map = new mapboxgl.Map({
      container: 'hotelMap', // container ID
      style: "mapbox://styles/mapbox/streets-v12",
      projection: {
        name: "mercator"
      },
      zoom: 2,
      center: [dataSourceRef.current[0].longitude, dataSourceRef.current[0].latitude]
    });
    mapRef.current = map
    map.addControl(new MapboxLanguage({
        defaultLanguage: 'zh-Hans'
    }));
    map.on("load", function() {
      $(".gha-mapbox").removeClass("loading")
      map.loadImage(
        dIcon,
        function(error, image) {
          if (error) throw error;
          // 添加图像到地图样式
          map.addImage('dIcon', image);
      })
      
      if (dataSourceRef.current.length > 10) {
        addCluster(map)
      } else {
        addMarkers(map)
      } 
    })
    $ghaMapHelper.fitBoundsMap(map, dataSourceRef.current.map(mark => [+mark.longitude, +mark.latitude]))
  }

  function addCluster(map) {
    map.addSource("hotel_markers", {
      type: "geojson",
      data: {
          type: "FeatureCollection",
          features: $ghaMapHelper.formatClusterPoint(dataSourceRef.current)
      },
      cluster: true,
      clusterMaxZoom: 14,
      clusterRadius: 50
    })
    map.addLayer({
      id: "clusters",
      type: "circle",
      source: "hotel_markers",
      filter: ["has", "point_count"],
      paint: {
          "circle-color": "#300B5C",
          "circle-radius": 18,
          "circle-opacity": .8
      }
    })
    map.addLayer({
      id: "cluster-count",
      type: "symbol",
      source: "hotel_markers",
      filter: ["has", "point_count"],
      paint: {
          "text-color": "#fff"
      },
      layout: {
          "text-field": "{point_count}",
          "text-size": 18
      }
    })
    map.addLayer({
      id: "unclustered-point",
      type: "circle",
      source: "hotel_markers",
      filter: ["!", ["has", "point_count"]],
      paint: {
          "circle-color": ["case", ["boolean", ["feature-state", "isActive"], !1], "#bbb0dc", "#300B5C"],
          "circle-radius": 18,
          "circle-opacity": ["case", ["boolean", ["feature-state", "isActive"], !1], 1, .8]
      }
    })
    map.addLayer({
      id: "unclustered-letter",
      type: "symbol",
      source: "hotel_markers",
      filter: ["!", ["has", "point_count"]],
      paint: {
          "icon-opacity": 1,
          "icon-translate": [1.3, -.7]
      },
      layout: {
          "icon-image": "dIcon",
          "icon-allow-overlap": !0
      }
    })
    var e = ["clusters", "cluster-count", "unclustered-point", "unclustered-letter"];
    map.on("mouseover", e, function() {
        return map.getCanvas().style.cursor = "pointer"
    }),
    map.on("mouseleave", e, function() {
        return map.getCanvas().style.cursor = ""
    })
    
    // 为未聚合的点添加点击事件和弹出窗口
    map.on("click", "unclustered-point", function(e) {
      const features = map.queryRenderedFeatures(e.point, { layers: ["unclustered-point"] });
      if (!features.length) return;
      
      const feature = features[0];
      const featureId = feature.properties.id;
      
      if (!featureId) return;

      $ghaMapHelper.flyMarkerToCenter(map, feature.geometry.coordinates)
      const hotelData = dataSourceRef.current.find(item => `${item.id}` === `${featureId}`)
      const popup = $ghaMapHelper.getPopup({hotelData, slug})
      popup.setLngLat(feature.geometry.coordinates).addTo(map)
      $ghaMapHelper.initPopupSwiper(hotelData)
      
    })
    map.on("click", "clusters", function(e) {
        var t, o = map.queryRenderedFeatures(e.point, {
            layers: ["clusters"]
        }), n = null === (t = o[0].properties) || void 0 === t ? void 0 : t.cluster_id;
        (null == map ? void 0 : map.getSource("hotel_markers")).getClusterExpansionZoom(n, function(e, t) {
            e || map.easeTo({
                center: o[0].geometry.coordinates,
                zoom: t + 1
            })
        })
    })
    map.addControl(new mapboxgl.NavigationControl(), "top-right")
  }

  function addMarkers(map) {
    dataSourceRef.current.forEach((item, idx) => {
      let markerEl
      if (slug === "offers") {
        markerEl = document.createElement('div')
        markerEl.className = 'gha-mapbox-marker-invalid'
        $(markerEl).append(`<i class="iconfont icon-Union"></i>`)
      } else {
        markerEl = document.createElement('div')
        markerEl.className = `gha-mapbox-marker-normal gha-mapbox-marker-${item.id}`
        $(markerEl).append(`
          <div class="">
            <p class="font-18">${ item.hotel_name }</p>
            <p class="font-18 hidden">${ 'CNY' } ${Math.floor(666)}</p>
            <p class="font-12 mt-1 hidden">最低费率</p>
          </div>
          <i class="iconfont ${item.is_collect ? 'icon-Heart-filled' : 'icon-Heart'} fav-icon"></i>
        `)
      }
      
      const marker = new mapboxgl.Marker(markerEl)
        .setLngLat([item.longitude, item.latitude])
        .addTo(map);
      marker.getElement().addEventListener("click", function() {
        $ghaMapHelper.flyMarkerToCenter(map, [item.longitude, item.latitude])
        const hotelData = dataSourceRef.current.find(d => `${d.id}` === `${item.id}`)
        const popup = $ghaMapHelper.getPopup({hotelData, slug})
        marker.setPopup(popup)
        marker.togglePopup()
        $ghaMapHelper.initPopupSwiper(hotelData)
      })
      $(marker.getElement()).find("i.fav-icon")[0]?.addEventListener('click', (event) => {
        // 
        event.stopPropagation(); // 阻止事件冒泡到地图
        marker.togglePopup()
        if (marker.getPopup()) {
          marker.getPopup().remove()
        }
        if ($(marker.getElement()).find("i.fav-icon").hasClass("loading")) return
        $(marker.getElement()).find("i.fav-icon").addClass("loading")
        onHotelLike(item.id, (nextState, _dataSource) => {
          $(marker.getElement()).find("i.fav-icon").removeClass("loading").removeClass('icon-Heart').removeClass('icon-Heart-filled')
          $(marker.getElement()).find("i.fav-icon").addClass(nextState ? "icon-Heart-filled" : "icon-Heart")
          dataSourceRef.current = _dataSource
        })
        console.log('Marker clicked, map click event will not fire');
      });
    })
  }

 
  return (
    <div className="g-main-content">
      <div className="fixed lg:static top-[90px] left-0 right-0 bottom-0 z-[1999] lg:z-[1000]">
        <div id="hotelMap" className="hotel-map-wrap h-[calc(100vh-90px)] relative z-[998] gha-mapbox loading">
          <div className="gha-mapbox-loading-el">
            <i className="iconfont icon-loading"></i>
          </div>
        </div>
        <HotelMapBar/>
      </div>
    </div>
  )
})

export default HotelMap