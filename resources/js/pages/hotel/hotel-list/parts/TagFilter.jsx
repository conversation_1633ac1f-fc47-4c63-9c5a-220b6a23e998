import mockjs from "mockjs"
import { useEffect, useMemo, useState } from "react"
import useSearchStore from "@/pages/hotel/hotel-list/store/useSearchStore";

export const filterTagList = [
  {
    type: "categories",
    name: "酒店类型",
    tags: [{id: "all", name: "显示全部"}].concat(window.__ServerVars__.categories)
  },
  {
    type: "brands",
    name: "酒店品牌",
    tags: [{id: "all", name: "显示全部"}].concat(window.__ServerVars__.brands)
  },
  {
    type: "series",
    name: "酒店系列",
    tags: [{id: "all", name: "显示全部"}].concat(window.__ServerVars__.series)
  },
  {
    type: "facts",
    name: "酒店设施",
    tags: [{id: "all", name: "显示全部"}].concat(window.__ServerVars__.facts)
  },
]

export default function TagFilter({onCondChange}) {
  const [showMoreBrands, setShowMoreBrands] = useState(false)
  const [moreBrandLen, setMoreBrandLen] = useState(5)
  const [filterSection] = useState(() => [...filterTagList])
  const selectTags = useSearchStore((state) => state.selectTags)

  const activeTags = useMemo(() => {
    return selectTags.map(e => {
      const [type, id] = e.split("_")
      return {...(filterSection.find(e => `${e.type}` === type)?.tags || []).find(e => `${e.id}` === id), type: type}
    }).filter(e => `${e.id}` !== "all")
  }, [selectTags, filterSection])
  useEffect(() => {
    function updateMoreBrandLen() {
      let offset = 0
      let nextLineIdx = 0
      const els = document.querySelectorAll(".virtual")
      for (let i = 0; i < els.length; i++) {
        // console.error("i=" + i, els[i].offsetHeight)
        if (els[i].offsetHeight > offset && offset > 0) {
          nextLineIdx = i
          break
        }
        offset = els[i].offsetHeight
      }
      setMoreBrandLen(nextLineIdx)
    }
    // console.error("nextLineIdx=" + nextLineIdx)
    window.onresize = function() {
      updateMoreBrandLen()
    }
    updateMoreBrandLen()
  }, [])
  function onSelectTag(row, tag) {
    let newSelectTags = [...selectTags]
    if (tag.id === "all") {
      newSelectTags = newSelectTags.filter(e => !e.startsWith(`${row.type}_`))
      newSelectTags.push(`${row.type}_all`)
    } else {
      if (newSelectTags.includes(`${row.type}_${tag.id}`)) {
        newSelectTags = newSelectTags.filter(e => e !== `${row.type}_${tag.id}`)
        if (newSelectTags.filter(e => e.startsWith(`${row.type}_`)).length === 0) {
          newSelectTags.push(`${row.type}_all`)
        }
      } else {
        newSelectTags = newSelectTags.filter(e => e !== `${row.type}_all`)
        newSelectTags.push(`${row.type}_${tag.id}`)
      }
    }
    useSearchStore.setState({selectTags: newSelectTags})
    onCondChange({type: "tags", value: newSelectTags})
  }
  function onRemoveTag(tag) {
    let newSelectTags = [...selectTags]
    newSelectTags = newSelectTags.filter(e => e !== `${tag.type}_${tag.id}`)
    if (newSelectTags.filter(e => e.startsWith(`${tag.type}_`)).length === 0) {
      newSelectTags.push(`${tag.type}_all`)
    }
    useSearchStore.setState({selectTags: newSelectTags})
    onCondChange({type: "tags", value: newSelectTags})
  }
  function onClearAllTag() {
    let newSelectTags = filterSection.map(e => `${e.type}_all`)
    useSearchStore.setState({selectTags: newSelectTags})
    onCondChange({type: "tags", value: newSelectTags})
  }
  return (
    <div className="pt-12">
      <div className="g-main-content">
        <div className="tag-filter-wrap">
          {filterSection.map((row, rowIdx) => {
            return (
              <div key={row.type} className="filter-row">
                <div className="filter-row-name">{row.name}</div>
                <div className="filter-row-tags-list relative">
                  {row.tags.slice(0, showMoreBrands ? 9999 : moreBrandLen).map(tag => {
                    return (
                      <div key={tag.id} onClick={() => onSelectTag(row, tag)} className={`tag-item ${selectTags.includes(`${row.type}_${tag.id}`) ? "active" : ""}`}>{tag.name}</div>
                    )
                  })}
                  {row.type === "brands" && (
                    <div className="tag-item more" onClick={() => setShowMoreBrands(!showMoreBrands)}>{showMoreBrands ? `收起` : `显示全部品牌(${row.tags.length - 1}个)`}</div>
                  )}
                  {row.type === "brands" && (
                    <>
                      {Array.from({length: row.tags.length}).map((_, index) => {
                        return (
                          <div key={index} className={`virtual virtual-${index} absolute top-0 left-0 right-0 opacity-0 -translate-x-[9999px]`}>
                            {row.tags.slice(0, index + 2).map(tag => {
                              return (
                                <div key={tag.id} className="tag-item">{tag.name}</div>
                              )
                            })}
                            {row.type === "brands" && <div className="tag-item more">显示全部品牌(28个)</div>}
                          </div>
                        )
                      })}
                    </>
                  )}
                </div>
              </div>
            )
          })}
        </div>
        {activeTags.length > 0 && (
          <div className="mt-2">
            <div className="flex flex-row flex-wrap -mx-1">
              {activeTags.map(e => {
                return (
                  <p key={e.id} className="mt-2 mx-1 py-1 pl-2.5 pr-2 rounded-full border border-primary text-primary font-12 flex items-center">
                    {e.name}
                    <i onClick={() => onRemoveTag(e)} className="iconfont icon-Close cursor-pointer ml-1"></i>
                  </p>
                )
              })}
              <p onClick={() => onClearAllTag()} className="mt-2 mx-1 py-1 pl-2.5 pr-2 rounded-full text-primary font-12 flex items-center cursor-pointer">清除</p>
            </div>
          </div>
        )}
      </div>
    </div>

  )
}
