import React, { useEffect, useRef, useState } from 'react'
import MobileFilterModal from './MobileFilterModal'
import useSearchStore from "@/pages/hotel/hotel-list/store/useSearchStore";


export default function MobileFilterBar({onSubmit}) {
  const targetRef = useRef(null);
  const sentinelRef = useRef(null);
  const [modalOpend, setModalOpend] = React.useState(false);
  useEffect(() => {
    window.addEventListener('scroll', () => {
      if (!sentinelRef.current || !targetRef.current) return;
      if (window.innerWidth < 1024 && sentinelRef.current.getBoundingClientRect().top < 146) {
        targetRef.current.classList.add('gha-fixed');
      } else {
        targetRef.current.classList.remove('gha-fixed');
      }
    })
  }, [])
  return (
    <>
      <div className="mobile-filter-bar">
        <div className="g-main-content">
          <div ref={sentinelRef} className="h-[56px]">
            <div ref={targetRef} className="flex flex-row items-center justify-between h-[56px] font-14">
              <p onClick={() => setModalOpend(true)} className="flex flex-row items-center cursor-pointer">筛选与排序 <i className="iconfont icon-filter ml-1 text-lg"></i></p>
              <p onClick={() => useSearchStore.setState({showType: "map"})} className="flex flex-row items-center cursor-pointer">显示地图 <i className="iconfont icon-Map ml-1 text-2xl"></i></p>
            </div>
          </div>
        </div>
      </div>
      <MobileFilterModal modalOpend={modalOpend} setModalOpend={setModalOpend} onSubmit={onSubmit}/>
    </>
  )
}