import { create } from "zustand"
import { filterTagList } from "@/pages/hotel/hotel-list/parts/TagFilter"
import { $helper } from "@/_utils/_index"


const useSearchStore = create((set) => {
  return {
    slug: window.__ServerVars__.slug,
    selectTags: (function() {
      const hash = location.hash.split("?")[1]
      const hashParams = $helper.decodeSearchHash(hash)
      return filterTagList.map(e => {
        if (e.type === "brands" && hashParams.brand_id) {
          return `brands_${hashParams.brand_id}`
        }
        return `${e.type}_all`
      })
    })(),
    order: "default",
    list: [],
    loading: true,
    showType: "list"
  }
})

export default useSearchStore;