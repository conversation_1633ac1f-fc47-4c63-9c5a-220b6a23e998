
$(function() {
  new AboutBrandDetailController()
})
class AboutBrandDetailController {
  constructor() {
    this.bootstrap()
  }

  bootstrap() {
    this.initBrandDetailDescTab()
    this.initBrandDetailSwiper('.brands-hotel-block')
    this.initBrandDetailSwiper('.brands-discount-block')
  }

  initBrandDetailSwiper(blockSelector) {
    new Swiper(`${blockSelector} .swiper-container`, {
      slidesPerView: 1.1,
      spaceBetween: 24,
      slidesPerGroup: 1,
      breakpoints: {
        768: {
          slidesPerView: 2,
        },
        1024: {
          slidesPerView: 3,
        },
      },
      on:{
        slideChange: function() {
          updateTransparentSlides(this);
          // console.error("this.slides", this.slides, this.activeIndex)
          // alert('改变了，activeIndex为'+this.activeIndex);
        },
        init: function() {
          updateTransparentSlides(this);
        },
      },
      navigation: {
        nextEl: `${blockSelector} .gha-swiper-button-next`,
        prevEl: `${blockSelector} .gha-swiper-button-prev`,
      },
      pagination: {
        el: `${blockSelector} .swiper-pagination`,
        clickable: true,
      },
    })

    function updateTransparentSlides(swiperInstance) {
      $(`${blockSelector} .swiper-cur-idx`).text(swiperInstance.activeIndex + 1)
      let slidesPerView = 3
      if (window.innerWidth < 1024) {
        slidesPerView = 2
      }
      if (window.innerWidth < 768) {
        slidesPerView = 1
      }
      // 移除所有透明类
      swiperInstance.slides.forEach((slide, idx) => {
        slide.classList.remove('transparent-slide-30');
        slide.classList.remove('transparent-slide-0');
        if (idx > swiperInstance.activeIndex + slidesPerView) {
          slide.classList.add("transparent-slide-0")
        }
        if (idx === swiperInstance.activeIndex + slidesPerView) {
          slide.classList.add("transparent-slide-30")
        }
        if (idx < swiperInstance.activeIndex - 1) {
          slide.classList.add("transparent-slide-0")
        }
        if (idx === swiperInstance.activeIndex - 1) {
          slide.classList.add("transparent-slide-30")
        }
      });
      
      // 计算目标索引
      const targetIndex = swiperInstance.activeIndex + 3;
      
      // 如果目标索引存在，则添加透明类
      if (targetIndex < swiperInstance.slides.length) {
        swiperInstance.slides[targetIndex].classList.add('transparent-slide-30');
      }
    }
  }

  initBrandDetailDescTab() {
    $(".brand-desc-tab-item a").on("click", function() {
      if ($(this).hasClass("active")) return
      const idx = $(this).parent().index()
      $(".brand-desc-tab-item a").removeClass("active")
      $(this).addClass("active")
      $(".brand-desc-tab-panel").addClass("hidden")
      $(".brand-desc-tab-panel").eq(idx).removeClass("hidden")
    })
  }
}