import { $http } from "@/_utils/_index"
$(function() {
  new AboutIndexController()
})
class AboutIndexController {
  constructor() {
    this.bootstrap()
  }

  bootstrap() {
    this.initBrandSwiper()
    this.initShowMoreBrand()
    this.initBrandDetailDescTab()
    this.initBrandDetailSwiper()
    this.initNewsLoadMore()
  }

  initNewsLoadMore() {
    $("body").on('click', ".news-load-more a", function() {
      console.error("load more")
      $(".loading-el").removeClass("hidden")
      $(".news-load-more").addClass("hidden")
      $http.getNewsList(window.source || "news_list", {year: window.curYear, page: window.page + 1}).subscribe(res => {
        $(".loading-el").addClass("hidden")
        if (res.status_code !== 200) {
          $(".news-load-more").removeClass("hidden")
          return
        }
        window.curCount = window.curCount + res.data.list.length
        if (window.curCount < res.data.total) {
          $(".news-load-more").removeClass("hidden")
        }
        let html = ""
        res.data.list.forEach($article => {
          html += `
            <a href="${$article.url}" class="news-item">
              <div class="info">
                <h1>${ $article['title'] }</h1>
                <p>${ $article['msg'] }</p>
                <div class="extra">
                  <span>${ $article['author'] }</span>
                  <span>${ $article['created_at'] }</span>
                </div>
              </div>
              ${$article['thumb'] ? `<div class="thumb" style="background-image: url(${ $article['thumb'] })"></div>` : ''}
            </a>
          `
        })
        $('.news-list').append(html)
      })
    })
  } 

  initBrandDetailSwiper() {
    // $('.brands-hotel-swiper').slick({
    //   slidesToShow: 3,
    //   slidesToScroll: 1,
    //   focusOnSelect: true,
    //   centerPadding: 24,
    // });
    // new Swiper(".brands-hotel-swiper .swiper-container", {
    //   slidesPerView: 3,
    //   spaceBetween: 24,
    //   centeredSlides: false,
    //   // slidesOffsetBefore: 20,
    //   // slidesOffsetAfter: 20,
    // })
  }

  initBrandDetailDescTab() {
    $(".brand-desc-tab-item a").on("click", function() {
      if ($(this).hasClass("active")) return
      const idx = $(this).parent().index()
      $(".brand-desc-tab-item a").removeClass("active")
      $(this).addClass("active")
      $(".brand-desc-tab-panel").addClass("hidden")
      $(".brand-desc-tab-panel").eq(idx).removeClass("hidden")
    })
  }

  initShowMoreBrand() {
    $(".show-more-brand-btn").on("click", function() {
      $(".brand-list-toggle").addClass("show-more")
      $(this).hide()
    })
  }

  initBrandSwiper() {
    new Swiper(".lg-swiper-el .swiper-container", {
      navigation: {
        nextEl: ".lg-swiper-el .gha-swiper-button-next",
        prevEl: ".lg-swiper-el .gha-swiper-button-prev",
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
    })
    new Swiper("#md-swiper")
  }

  initAccountSwiper() {
    new Swiper(".account-section-wrap .swiper-lg", {
      slidesPerView: "auto",
      spaceBetween: 16,
      centeredSlides: true,
      
      // loop: true,
      navigation: {
        nextEl: ".account-section-wrap .gha-swiper-button-next",
        prevEl: ".account-section-wrap .gha-swiper-button-prev",
      },
      
    })
    new Swiper(".account-section-wrap .swiper-m", {
      slidesPerView: "1.2",
      spaceBetween: 16,
      slidesOffsetBefore: 16,
    })
  }
}