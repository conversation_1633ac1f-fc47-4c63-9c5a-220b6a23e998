@extends('layouts.main-layout', ["layoutRootClassName" => "transparent-header"])
@section("styles")
    <link rel="stylesheet" href="{{ asset('vendors/swiper/swiper-bundle.min.css') }}">
@endsection

@section('content')
  <div class="membership-page-wrap">
    <div class="top-banner" style="background-image: url({{ Vite::asset('resources/images/membership/d-plan/banner.png') }})">
      <div class="g-main-content-sm">
        <div class="text-white pt-32 md:pt-64">
          <div class="inline-block">
            <h1 class="font-36">GHA探索之旅</h1>
            <h1 class="font-36 last-justify">会员礼遇</h1>
          </div>
        </div>
      </div>
    </div>
    <div class="py-16">
      <div class="g-main-content-sm">
        <div class="text-center">
          <h4 class="font-24">入会即享会员回馈</h4>
          <p class="font-15 !leading-[28px] mt-4">
            通过GHA探索之旅，您可以更快速轻松地提高您的回馈和礼遇。<br/>
            每位会员从入会首日起就能赚取DISCOVERY奖励金（D$），但晋级速度取决于您自己。 我们的会员级别分为四级，让您有机会更快升至 VIP 级别。 <br/>
            有 3 种灵活的升级方式可供选择：住宿房晚/次数、合格消费或入住的酒店品牌数量。 您的 GHA探索之旅尊享服务、礼遇和回馈会随着级别的提高而增加。<br/>
            您可赚取及用于下次住宿的DISCOVERY奖励金（D$）百分比也是如此。<br/>
            准备好了吗?
          </p>
          <div class="mt-6">
            <a href="{{ route('auth.register') }}" class="gha-primary-btn !px-10">立即免费加入</a>
          </div>
        </div>
      </div>
    </div>
    <div class="py-16 hidden lg:block bg-[#f1f1f1]/70">
      <div class="g-main-content">
        <div class="flex flex-col md:flex-row lg:-mx-2 xl:-mx-5">
          @foreach($ghaMockMembershipRights as $card)
          <div class="flex-1 lg:px-2 xl:px-5 mt-8 md:mt-0">
            <img class="w-full" src="{{ Vite::asset('resources/images/membership/index/card-'. $card['key'] .'.png') }}" alt="">
            <div class="text-center text-[#333] font-14 rounded-t-xl pt-4 px-4 mt-2  bg-gradient-to-b {{ 'right-card-'. $card['key'] .'-bg' }} to-transparent">
              <div class="gha-divider inline-flex w-32 before:bg-[#999]/20 after:bg-[#999]/20"><h5 class="px-4">{{ $card['title'] }}</h5></div>
            </div>
            <div class="mt-3">
              @foreach($card['rights'] as $item)
              <div class="flex flex-row items-center py-2">
                <div class="w-12 h-12 shrink-0 flex items-center justify-center rounded-full shadow-[0px_2px_4px_2px_rgba(0,0,0,0.05)] {{ ($item['active'] ?? 0) ? "bg-primary text-white" : "" }}"><i class="iconfont {{ $item['icon'] }} font-28"></i></div>
                <p class="font-14 ml-2">{{ $item['text'] }}</p>
              </div>
              @endforeach
              {{-- <div class="flex flex-row items-center">
                <div class="w-12 h-12 flex items-center justify-center rounded-full shadow-[0px_2px_4px_2px_rgba(0,0,0,0.05)]"><i class="iconfont icon-a-SpendDonEligiblePurchases font-28"></i></div>
                <p class="font-14 ml-2">赚取DISCOVERY奖励金（D$） (4%)</p>
              </div> --}}
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>
    <div class="py-6 block lg:hidden bg-[#f9f9f9] mobile-card-rights">
      <div class="g-main-content">
        <div class="user-page-wrap !bg-white rounded-xl overflow-hidden">
          <div class="user-dashboard-wrap">
            <div class="p-4">
              <div class="level-step-container active-1">
                <div class="line-track">
                  <span></span>
                </div>
                <div class="level-list">
                  @foreach($ghaMockMembershipRights as $card)
                    <div class="level-item">
                      <h3>{{ $card['title'] }}</h3>
                      <div class="tracker" data-key="{{ $card['key'] }}"><span></span></div>
                      <div class="level-card">
                        <img src="{{ Vite::asset('resources/images/membership/index/card-'. $card['key'] .'.png') }}" alt="" />
                      </div>
                    </div>
                  @endforeach
                </div>
                <div class="level-card-m">
                  @foreach($ghaMockMembershipRights as $card)
                    <img src="{{ Vite::asset('resources/images/membership/index/card-'. $card['key'] .'.png') }}" alt="" />
                  @endforeach
                </div>
              </div>
              @foreach($ghaMockMembershipRights as $card)
              <div class="mt-8 pb-8 card-panel card-panel-{{$card['key']}} {{ $card['key'] != 1 ? 'hidden' : ''}}">
                <div class="gha-divider"><h4 class="font-bold font-12 px-4">{{$card['title']}}会员福利</h4></div>
                <div class="flex flex-row flex-wrap -mx-1.5 lg:-mx-2.5 -mt-2 lg:mt-8 px-4 lg:px-16">
                  @foreach($card['rights'] as $item)
                    <div class="w-1/2 lg:w-1/4 flex flex-col items-center mt-4 px-1.5 lg:px-2.5">
                      <div class="w-11 h-11 rounded-full shadow-[0px_2px_4px_2px_rgba(0,0,0,0.05)] flex items-center justify-center {{($item['active'] ?? 0) ? "bg-primary text-white" : ""}}">
                        <i class="iconfont {{$item['icon']}} text-2xl"></i>
                      </div>
                      <p class="text-center mt-2.5 font-12">{{$item['text']}}</p>
                    </div>
                  @endforeach
                </div>
              </div>
              @endforeach
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
@endsection


@section('scripts')
  <script src="{{ asset('vendors/swiper/swiper-bundle.min.js') }}"></script>
  @vite("resources/js/pages/membership/index.js")
@endsection
