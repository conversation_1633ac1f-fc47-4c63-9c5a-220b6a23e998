<div class="hotel-item hotel-item-large hotel-room" data-id="{{ $room_item['id'] }}">
    <div class="cover-wrap">
        <div class="cover-swiper ignore-opacity">
            <div class="gha-swiper-button gha-swiper-button-prev"><i class="iconfont icon-a-Arrow-Left"></i></div>
            <div class="gha-swiper-button gha-swiper-button-next"><i class="iconfont icon-a-Arrow-Right"></i></div>
            <div class="swiper-pagination"></div>
            <div class="swiper-pagination-nums"><span class="cur">1</span>/{{ count($room_item['images']) }}</div>
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    @foreach($room_item['images'] as $image)
                    <div class="swiper-slide relative">
                        <img class="w-full h-full" src="{{ $image }}" alt="" loading="lazy">
                        <div class="hotel-image-mask"></div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
    <div class="tag-wrap">
        <p>{{ $room_item['head_line'] ?? '标签标签标签标签' }}</p>
    </div>
    <div class="info-wrap">
    <h5 class="brand">{{ $room_item["brand_name"] ?? "品牌名" }}</h5>
    <h2 class="title">{{ $room_item["room_name"] ?? '房型标题' }}</h2>
    @if($room_item['size'])
    <h5 class="room-area">
        <i class="iconfont icon-mianji"></i>
        {{ $room_item['size'] }}
    </h5>
    @endif
    <div class="spt-line"></div>
    <div class="price-wrap">
        <div class="protrude">
        <p>会员价低至</p>
        <p class="price-num">CNY{{ $room_item['price'] ?? 1999 }}</p>
        </div>
        <div class="">
        <p>会员价低至</p>
        <p class="price-num">CNY{{ $room_item['price_membership'] ?? 1999 }}</p>
        </div>
    </div>
    <div class="action-wrap">
        <a href="" class="gha-primary-btn !py-1">立即预订</a>
    </div>
    </div>
</div>