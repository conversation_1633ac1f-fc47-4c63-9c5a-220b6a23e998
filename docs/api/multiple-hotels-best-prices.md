# 批量查询多个酒店最佳价格接口

## 接口概述

此接口用于批量查询多个酒店的最佳价格，返回每个酒店的最低价格信息。适用于酒店列表页面或比价功能。

## 接口信息

- **URL**: `/api/v1/sabre/multipleHotelsBestPrices`
- **方法**: `POST`
- **Content-Type**: `application/json`

## 请求参数

### 必需参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| hotelIds | array | 酒店ID列表，最多20个 | [100823, 100824, 100825] |
| startDate | string | 入住日期，格式：YYYY-MM-DD | "2025-07-09" |
| endDate | string | 离店日期，格式：YYYY-MM-DD | "2025-07-11" |

### 可选参数

| 参数名 | 类型 | 默认值 | 说明 | 示例 |
|--------|------|--------|------|------|
| numRooms | integer | 1 | 房间数量，1-10 | 1 |
| adults | integer | 1 | 成人数量，1-20 | 2 |
| children | integer | 0 | 儿童数量，0-10 | 1 |
| childrenAge | string | null | 儿童年龄，格式：age1,age2;age3,age4 | "8,10" |
| accessCode | string | null | 促销代码 | "SUMMER2025" |

## 请求示例

```json
{
    "hotelIds": [100823, 100824, 100825],
    "startDate": "2025-07-09",
    "endDate": "2025-07-11",
    "numRooms": 1,
    "adults": 2,
    "children": 1,
    "childrenAge": "8",
    "accessCode": "SUMMER2025"
}
```

## 响应格式

### 成功响应

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "success": true,
        "hotels": [
            {
                "hotelId": 100823,
                "bestPrice": {
                    "currency": "USD",
                    "originalPrice": 25000,
                    "cnyPrice": 180000,
                    "priceDisplay": "¥1,800.00",
                    "rateCode": "BAR",
                    "rateName": "Best Available Rate",
                    "isMemberRate": false,
                    "guaranteePolicy": "GCC_CRD",
                    "cancelRuleString": "可免费取消至入住前24小时"
                },
                "room": {
                    "roomCode": "STD",
                    "roomName": "标准房",
                    "roomDescription": "舒适的标准客房，配备现代化设施"
                },
                "availableRoomsCount": 3,
                "totalRatesCount": 5
            },
            {
                "hotelId": 100824,
                "bestPrice": {
                    "currency": "USD",
                    "originalPrice": 28000,
                    "cnyPrice": 201600,
                    "priceDisplay": "¥2,016.00",
                    "rateCode": "MEM",
                    "rateName": "Member Rate",
                    "isMemberRate": true,
                    "guaranteePolicy": "GCC_CRD",
                    "cancelRuleString": "可免费取消至入住前48小时"
                },
                "room": {
                    "roomCode": "DLX",
                    "roomName": "豪华房",
                    "roomDescription": "宽敞的豪华客房，享有城市景观"
                },
                "availableRoomsCount": 2,
                "totalRatesCount": 4
            }
        ],
        "total_count": 2,
        "errors": [
            {
                "hotelId": 100825,
                "error": "该酒店在指定日期无可用房间"
            }
        ],
        "query_params": {
            "startDate": "2025-07-09",
            "endDate": "2025-07-11",
            "numRooms": 1,
            "adults": 2,
            "children": 1,
            "accessCode": "***"
        }
    }
}
```

### 错误响应

```json
{
    "code": 400,
    "message": "参数验证失败",
    "data": {
        "hotelIds": ["酒店ID列表不能为空"],
        "startDate": ["入住日期必须是未来日期"]
    }
}
```

## 响应字段说明

### hotels 数组字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| hotelId | integer | 酒店ID |
| bestPrice | object | 最佳价格信息 |
| room | object | 对应的房型信息 |
| availableRoomsCount | integer | 可用房型数量 |
| totalRatesCount | integer | 总价格方案数量 |

### bestPrice 对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| currency | string | 原始货币代码 |
| originalPrice | integer | 原始价格（分） |
| cnyPrice | integer | 人民币价格（分） |
| priceDisplay | string | 格式化的价格显示 |
| rateCode | string | 价格代码 |
| rateName | string | 价格名称 |
| isMemberRate | boolean | 是否为会员价格 |
| guaranteePolicy | string | 担保政策 |
| cancelRuleString | string | 取消政策描述 |

### room 对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| roomCode | string | 房型代码 |
| roomName | string | 房型名称 |
| roomDescription | string | 房型描述 |

## 使用场景

1. **酒店列表页面**: 显示多个酒店的起始价格
2. **比价功能**: 快速比较多个酒店的最低价格
3. **搜索结果**: 在搜索结果中显示价格信息
4. **推荐系统**: 为用户推荐性价比高的酒店

## 注意事项

1. 一次最多可查询20个酒店
2. 接口会并发查询多个酒店，提高响应速度
3. 如果某个酒店查询失败，会在errors数组中返回错误信息
4. 价格信息会自动转换为人民币显示
5. 支持促销代码查询，会优先返回促销价格
6. 接口具有缓存机制，相同参数的查询会使用缓存结果

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 参数验证失败 |
| 500 | 系统内部错误 |

## 性能优化

- 接口支持缓存，相同查询参数会复用缓存结果
- 并发查询多个酒店，减少总响应时间
- 自动过滤无效的查询结果
- 支持部分成功返回，即使部分酒店查询失败也会返回成功的结果
