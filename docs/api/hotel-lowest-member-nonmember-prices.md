# 查询单个酒店最低会员价和非会员价接口

## 接口描述

该接口用于查询单个酒店的最低会员价和对应的最低非会员价，并提供价格对比信息。适用于酒店详情页面或列表页面显示会员优惠信息。

## 接口信息

- **URL**: `/api/v1/sabre/hotelLowestMemberAndNonMemberPrices`
- **方法**: `POST`
- **认证**: 需要
- **Content-Type**: `application/json`

## 请求参数

### 必需参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| hotelId | integer | 酒店ID | 100823 |
| startDate | string | 入住日期 (YYYY-MM-DD) | "2025-07-09" |
| endDate | string | 离店日期 (YYYY-MM-DD) | "2025-07-11" |

### 可选参数

| 参数名 | 类型 | 默认值 | 说明 | 示例 |
|--------|------|--------|------|------|
| numRooms | integer | 1 | 房间数量 (1-10) | 1 |
| adults | integer | 1 | 成人数量 (1-20) | 2 |
| children | integer | 0 | 儿童数量 (0-10) | 1 |
| childrenAge | string | null | 儿童年龄，多个年龄用逗号分隔 | "8,12" |
| accessCode | string | null | 促销代码 | "SUMMER2025" |

## 请求示例

```json
{
    "hotelId": 100823,
    "startDate": "2025-07-09",
    "endDate": "2025-07-11",
    "numRooms": 1,
    "adults": 2,
    "children": 1,
    "childrenAge": "8",
    "accessCode": "SUMMER2025"
}
```

## 响应格式

### 成功响应

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "success": true,
        "hotel": {
            "hotelId": 100823,
            "memberPrice": {
                "currency": "USD",
                "originalPrice": 22000,
                "cnyPrice": 158400,
                "priceDisplay": "¥1,584.00",
                "rateCode": "MEM",
                "rateName": "Member Rate",
                "guaranteePolicy": "GCC_CRD",
                "cancelRuleString": "可免费取消至入住前24小时"
            },
            "memberRoom": {
                "roomCode": "STD",
                "roomName": "标准房",
                "roomDescription": "舒适的标准客房，配备现代化设施"
            },
            "nonMemberPrice": {
                "currency": "USD",
                "originalPrice": 25000,
                "cnyPrice": 180000,
                "priceDisplay": "¥1,800.00",
                "rateCode": "BAR",
                "rateName": "Best Available Rate",
                "guaranteePolicy": "GCC_CRD",
                "cancelRuleString": "可免费取消至入住前24小时"
            },
            "nonMemberRoom": {
                "roomCode": "STD",
                "roomName": "标准房",
                "roomDescription": "舒适的标准客房，配备现代化设施"
            },
            "priceComparison": {
                "memberSavings": 21600,
                "memberSavingsDisplay": "¥216.00",
                "discountPercentage": 12.0,
                "discountPercentageDisplay": "12%"
            },
            "availableRoomsCount": 3,
            "totalRatesCount": 5
        },
        "query_params": {
            "hotelId": 100823,
            "startDate": "2025-07-09",
            "endDate": "2025-07-11",
            "numRooms": 1,
            "adults": 2,
            "children": 1,
            "accessCode": "***"
        }
    }
}
```

### 错误响应

```json
{
    "code": 400,
    "message": "参数验证失败",
    "data": {
        "hotelId": ["酒店ID不能为空"],
        "startDate": ["入住日期必须是未来日期"]
    }
}
```

### 无可用房间响应

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "success": false,
        "message": "该酒店在指定日期无可用房间或价格",
        "hotelId": 100823,
        "query_params": {
            "hotelId": 100823,
            "startDate": "2025-07-09",
            "endDate": "2025-07-11",
            "numRooms": 1,
            "adults": 2,
            "children": 0,
            "accessCode": null
        }
    }
}
```

## 响应字段说明

### hotel 对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| hotelId | integer | 酒店ID |
| memberPrice | object | 最低会员价格信息 |
| memberRoom | object | 会员价对应的房型信息 |
| nonMemberPrice | object | 最低非会员价格信息 |
| nonMemberRoom | object | 非会员价对应的房型信息 |
| priceComparison | object | 价格对比信息 |
| availableRoomsCount | integer | 可用房型数量 |
| totalRatesCount | integer | 总价格方案数量 |

### memberPrice/nonMemberPrice 对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| currency | string | 原始货币代码 |
| originalPrice | integer | 原始价格（分） |
| cnyPrice | integer | 人民币价格（分） |
| priceDisplay | string | 格式化的价格显示 |
| rateCode | string | 价格代码 |
| rateName | string | 价格名称 |
| guaranteePolicy | string | 担保政策 |
| cancelRuleString | string | 取消政策描述 |

### memberRoom/nonMemberRoom 对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| roomCode | string | 房型代码 |
| roomName | string | 房型名称 |
| roomDescription | string | 房型描述 |

### priceComparison 对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| memberSavings | integer | 会员节省金额（分） |
| memberSavingsDisplay | string | 格式化的节省金额显示 |
| discountPercentage | float | 折扣百分比 |
| discountPercentageDisplay | string | 格式化的折扣百分比显示 |

## 注意事项

1. **价格可能为空**: 如果酒店没有会员价或非会员价，对应字段将不会出现在响应中
2. **价格对比**: 只有当同时存在会员价和非会员价时，才会有priceComparison字段
3. **缓存机制**: 查询结果会被缓存，提高响应速度
4. **实时价格**: 价格信息实时从Sabre API获取，可能存在延迟
5. **房型匹配**: 会员价和非会员价可能对应不同的房型

## 使用场景

- 酒店详情页面显示会员优惠信息
- 酒店列表页面显示价格对比
- 会员权益展示
- 促销活动页面

## 性能建议

- 相同参数的重复查询会使用缓存，响应更快
- 避免频繁查询，建议实现客户端缓存机制
- 建议在用户真正需要时才调用此接口
