# Sabre Channel Connect C1 API 集成文档

本文档描述了如何使用项目中集成的Sabre Channel Connect C1 API服务。

## 概述

Sabre Channel Connect C1 API集成包含以下主要功能：
- 酒店可用性查询
- 预订创建、查询、修改和取消
- 酒店详情查询
- 支付方式查询
- 缓存管理

## 配置

### 1. 环境变量配置

将以下配置添加到你的 `.env` 文件中：

```env
# Sabre API 认证配置
SABRE_AUTH_URL=https://oscp.stage.ghaloyalty.com/api/v3/auth/token
SABRE_USERNAME=your_username_here
SABRE_PASSWORD=your_password_here
SABRE_API_KEY=your_api_key_here

# Sabre API 基础URL
SABRE_API_URL=https://services-c1.synxis.com

# 默认配置
SABRE_PRIMARY_CHANNEL=SYDC
SABRE_SECONDARY_CHANNEL=DSCVRYLYLTY
SABRE_CHAIN_ID=32446
SABRE_CONTEXT=WBSVC
SABRE_LANGUAGE=zh-CN

# 缓存配置
SABRE_CACHE_ENABLED=false  # 本地开发建议设为false，生产环境设为true
SABRE_TOKEN_CACHE_TTL=3300  # 令牌缓存时间（秒）
SABRE_AVAILABILITY_CACHE_TTL=300  # 可用性查询缓存时间（秒）
SABRE_HOTEL_DETAILS_CACHE_TTL=3600  # 酒店详情缓存时间（秒）
```

### 2. 服务提供者注册

在 `config/app.php` 中注册服务提供者：

```php
'providers' => [
    // ...
    App\Providers\SabreServiceProvider::class,
],
```

### 3. 发布配置文件

```bash
php artisan vendor:publish --tag=sabre-config
```

## 服务类说明

### SabreService
主要的Sabre API服务类，处理认证和基础API调用。

### SabreAvailabilityService
处理酒店可用性查询的专门服务类。

### SabreReservationService
处理预订相关操作的专门服务类。

### SabreHotelService
处理酒店信息查询的专门服务类。

## API端点

### 1. 查询酒店可用性

```http
POST /api/v1/sabre/availability
```

**请求参数：**
```json
{
    "hotelId": 100823,
    "startDate": "2025-07-09",
    "endDate": "2025-07-11",
    "numRooms": 1,
    "adults": 1,
    "children": 2,
    "childrenAge": "6,10",  // 儿童年龄：6岁和10岁，多个年龄用逗号分隔
    "loyaltyProgram": "GHA",
    "loyaltyLevel": "RED",
    "accessType": "Promotion",
    "accessCode": "PROMO123",
    "onlyPromo": false
}
```

### 2. 创建预订（简化版本）

```http
POST /api/v1/sabre/createReservation
```

**请求参数（简化后）：**
```json
{
    "hotelId": 100823,
    "checkInDate": "2025-07-09",
    "checkOutDate": "2025-07-11",
    "numRooms": 1,
    "adults": 2,
    "children": 1,
    "childrenAges": [8],
    "roomCode": "STD",
    "rateCode": "BAR",
    "primaryGuest": {
        "firstName": "张",
        "lastName": "三",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "address": {
            "line1": "北京市朝阳区某某街道123号",
            "city": "北京",
            "state": "北京",
            "country": "CN",
            "postalCode": "100000"
        }
    },
    "payment": {
        "cardType": "VI",
        "cardNumber": "****************",
        "cardHolder": "ZHANG SAN",
        "expiryMonth": 12,
        "expiryYear": 2025
    },
    "loyaltyNumber": "GHA123456789",
    "promoCode": "PROMO2025",
    "specialRequests": "高层房间，安静环境",
    "sendConfirmationEmail": true
}
```

**参数说明：**

**必填参数：**
- `hotelId`: 酒店ID
- `checkInDate`: 入住日期 (YYYY-MM-DD)
- `checkOutDate`: 退房日期 (YYYY-MM-DD)
- `roomCode`: 房型代码
- `rateCode`: 价格代码
- `primaryGuest`: 主要客人信息
  - `firstName`: 名
  - `lastName`: 姓
  - `email`: 邮箱
  - `phone`: 电话
- `payment`: 支付信息
  - `cardType`: 卡类型 (VI=Visa, MC=MasterCard, AX=American Express, DC=Diners Club)
  - `cardNumber`: 卡号
  - `cardHolder`: 持卡人姓名
  - `expiryMonth`: 过期月份 (1-12)
  - `expiryYear`: 过期年份

**可选参数：**
- `numRooms`: 房间数量 (默认: 1)
- `adults`: 成人数量 (默认: 1)
- `children`: 儿童数量 (默认: 0)
- `childrenAges`: 儿童年龄数组
- `primaryGuest.address`: 地址信息
- `loyaltyNumber`: 忠诚度会员号
- `promoCode`: 促销代码
- `specialRequests`: 特殊要求
- `sendConfirmationEmail`: 是否发送确认邮件 (默认: false)

**后端自动处理的固定参数：**
- `chainId`: 自动使用配置的连锁酒店ID (32446)
- 渠道信息 (SYDC, DSCVRYLYLTY)
- 语言设置 (zh-CN)
- 市场来源代码 (GHA)
- 时间格式转换 (自动转换为UTC ISO格式)
- 完整的客人信息结构
- 支付信息的完整格式

### 优化说明

**优化前的问题：**
1. 客户端需要传递大量固定参数（chainId、渠道信息等）
2. 复杂的嵌套结构（guests数组包含PersonName、EmailAddress、ContactNumbers等）
3. 时间格式需要客户端处理为UTC ISO格式
4. 支付信息格式复杂（ExpireDate需要特殊格式）
5. 地址信息结构复杂且冗余

**优化后的改进：**
1. **参数简化**: 客户端只需传递业务必需的参数
2. **扁平化结构**: 减少嵌套层级，使用简单的对象结构
3. **智能默认值**: 后端自动填充系统配置的固定参数
4. **格式自动转换**: 后端处理时间格式、支付卡格式等转换
5. **向后兼容**: 保留原有复杂接口，新增简化接口

**参数数量对比：**
- 优化前: 需要传递约50+个字段
- 优化后: 只需传递约15个核心字段
- 减少约70%的参数复杂度

### 3. 查询预订

```http
GET /api/v1/sabre/reservation?itineraryNumber=123456
```

或

```http
GET /api/v1/sabre/reservation?confirmationNumber=ABC123&hotelId=100823
```

### 4. 取消预订

```http
DELETE /api/v1/sabre/reservation
```

**请求参数：**
```json
{
    "confirmationNumber": "ABC123",
    "hotelId": 100823,
    "hotelCode": "HOTEL001"
}
```

### 5. 获取酒店详情

```http
GET /api/v1/sabre/hotel/100823
```

### 6. 获取酒店支付方式

```http
GET /api/v1/sabre/hotel/100823/payment-methods
```

### 7. 清除缓存

```http
DELETE /api/v1/sabre/cache
```

**请求参数：**
```json
{
    "type": "all",  // 可选值: "all", "availability", "hotel"
    "hotelId": 100823  // 清除特定酒店缓存时使用
}
```

## 使用示例

### 在控制器中使用服务

```php
<?php

namespace App\Http\Controllers;

use App\Services\SabreAvailabilityService;
use App\Services\SabreReservationService;
use App\Exceptions\SabreApiException;

class BookingController extends Controller
{
    public function searchHotels(Request $request, SabreAvailabilityService $availabilityService)
    {
        try {
            $params = [
                'hotelId' => $request->input('hotelId'),
                'startDate' => $request->input('startDate'),
                'endDate' => $request->input('endDate'),
                'numRooms' => $request->input('numRooms', 1),
                'adults' => $request->input('adults', 1),
            ];

            $result = $availabilityService->checkMemberAndNonMemberRates($params);
            $parsed = $availabilityService->parseAvailabilityResponse($result);

            return response()->json([
                'success' => true,
                'data' => $parsed
            ]);

        } catch (SabreApiException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getUserMessage(),
                'is_retryable' => $e->isRetryable()
            ], 500);
        }
    }
}
```

### 直接使用服务类

```php
use App\Services\SabreReservationService;

$reservationService = app(SabreReservationService::class);

// 创建预订
$reservationData = [
    'hotelId' => 100823,
    'guests' => [...],
    'roomStay' => [...],
    // ...
];

$result = $reservationService->createSingleRoomBookingWithMembership($reservationData);
```

## 错误处理

所有Sabre API调用都可能抛出 `SabreApiException` 异常。该异常提供了以下有用的方法：

- `getUserMessage()`: 获取用户友好的错误消息
- `isRetryable()`: 判断是否为可重试的错误
- `isAuthError()`: 判断是否为认证错误
- `getContext()`: 获取错误上下文信息

## 缓存

系统自动缓存以下数据：
- 认证令牌（55分钟）
- 可用性查询结果（5分钟）
- 酒店详情（1小时）

可以通过API端点或直接调用服务方法来清除缓存。

## 测试

运行测试：

```bash
php artisan test tests/Feature/SabreServiceTest.php
```

注意：测试需要配置有效的Sabre API凭据才能正常运行。

## 缓存配置详解

### 本地开发环境
在本地开发时，建议禁用缓存以便实时看到API响应：

```env
SABRE_CACHE_ENABLED=false
```

### 生产环境
在生产环境中，建议启用缓存以提高性能：

```env
SABRE_CACHE_ENABLED=true
SABRE_TOKEN_CACHE_TTL=3300      # 认证令牌缓存55分钟
SABRE_AVAILABILITY_CACHE_TTL=300 # 可用性查询缓存5分钟
SABRE_HOTEL_DETAILS_CACHE_TTL=3600 # 酒店详情缓存1小时
```

### 手动控制缓存
也可以在代码中动态控制缓存：

```php
// 临时禁用缓存
config(['sabre.cache.enabled' => false]);

// 清除特定缓存
$availabilityService->clearAvailabilityCache();
$hotelService->clearHotelDetailsCache($hotelId);
```

## 儿童年龄处理

### 基本格式
- **单个儿童**: `"childrenAge": "8"`
- **多个儿童**: `"childrenAge": "6,10,12"`
- **多房间**: `"childrenAge": "6,10;8"` (分号分隔不同房间)

### 完整示例
```json
{
    "hotelId": 100823,
    "startDate": "2025-07-09",
    "endDate": "2025-07-11",
    "numRooms": 1,
    "adults": 2,
    "children": 2,
    "childrenAge": "6,10"
}
```

### 预订中的儿童信息
```json
{
    "roomStay": {
        "GuestCount": [
            {
                "AgeQualifyingCode": "Adult",
                "NumGuests": 2
            },
            {
                "AgeQualifyingCode": "Child",
                "NumGuests": 2,
                "Ages": [6, 10]
            }
        ]
    }
}
```

详细的儿童年龄处理说明请参考：[儿童年龄处理文档](CHILDREN_AGE_HANDLING.md)

## 注意事项

1. 确保服务器时间与Sabre服务器时间同步
2. 生产环境中建议启用SSL证书验证
3. 监控API调用频率，避免超出限制
4. 定期检查和更新API凭据
5. 在高并发环境中考虑使用队列处理API调用
6. **本地开发时建议禁用缓存** (`SABRE_CACHE_ENABLED=false`)
7. 正确处理儿童年龄格式，确保与儿童数量匹配
