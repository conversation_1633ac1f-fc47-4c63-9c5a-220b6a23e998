<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\SabreAvailabilityService;
use App\DTOs\RoomAvailabilityResponseDTO;
use App\DTOs\RoomDTO;
use App\DTOs\RoomRateDTO;

class HotelLowestMemberNonMemberPricesTest extends TestCase
{
    /**
     * 测试查询单个酒店最低会员价和非会员价接口
     */
    public function test_hotel_lowest_member_nonmember_prices_success()
    {
        // 模拟请求数据
        $requestData = [
            'hotelId' => 100823,
            'startDate' => '2025-07-09',
            'endDate' => '2025-07-11',
            'numRooms' => 1,
            'adults' => 2
        ];

        // 模拟SabreAvailabilityService返回数据
        $mockService = $this->createMock(SabreAvailabilityService::class);
        $mockService->method('checkHotelLowestMemberAndNonMemberPrices')
                   ->willReturn([
                       'success' => true,
                       'hotel' => [
                           'hotelId' => 100823,
                           'memberPrice' => [
                               'currency' => 'USD',
                               'originalPrice' => 22000,
                               'cnyPrice' => 158400,
                               'priceDisplay' => '¥1,584.00',
                               'rateCode' => 'MEM',
                               'rateName' => 'Member Rate',
                               'guaranteePolicy' => 'GCC_CRD',
                               'cancelRuleString' => '可免费取消至入住前24小时'
                           ],
                           'nonMemberPrice' => [
                               'currency' => 'USD',
                               'originalPrice' => 25000,
                               'cnyPrice' => 180000,
                               'priceDisplay' => '¥1,800.00',
                               'rateCode' => 'BAR',
                               'rateName' => 'Best Available Rate',
                               'guaranteePolicy' => 'GCC_CRD',
                               'cancelRuleString' => '可免费取消至入住前24小时'
                           ],
                           'priceComparison' => [
                               'memberSavings' => 21600,
                               'memberSavingsDisplay' => '¥216.00',
                               'discountPercentage' => 12.0,
                               'discountPercentageDisplay' => '12%'
                           ]
                       ],
                       'query_params' => [
                           'hotelId' => 100823,
                           'startDate' => '2025-07-09',
                           'endDate' => '2025-07-11'
                       ]
                   ]);

        $this->app->instance(SabreAvailabilityService::class, $mockService);

        // 发送请求
        $response = $this->postJson('/api/v1/sabre/hotelLowestMemberAndNonMemberPrices', $requestData);

        // 验证响应
        $response->assertStatus(200)
                ->assertJson([
                    'code' => 200,
                    'message' => 'success',
                    'data' => [
                        'success' => true
                    ]
                ]);

        // 验证响应结构
        $responseData = $response->json('data');
        $this->assertArrayHasKey('hotel', $responseData);
        $this->assertArrayHasKey('query_params', $responseData);

        // 验证酒店数据结构
        $hotel = $responseData['hotel'];
        $this->assertArrayHasKey('hotelId', $hotel);

        if (isset($hotel['memberPrice'])) {
            $this->assertArrayHasKey('priceDisplay', $hotel['memberPrice']);
            $this->assertArrayHasKey('rateName', $hotel['memberPrice']);
        }

        if (isset($hotel['nonMemberPrice'])) {
            $this->assertArrayHasKey('priceDisplay', $hotel['nonMemberPrice']);
            $this->assertArrayHasKey('rateName', $hotel['nonMemberPrice']);
        }

        if (isset($hotel['priceComparison'])) {
            $this->assertArrayHasKey('memberSavings', $hotel['priceComparison']);
            $this->assertArrayHasKey('discountPercentage', $hotel['priceComparison']);
        }
    }

    /**
     * 测试参数验证失败
     */
    public function test_validation_fails_with_missing_required_params()
    {
        $requestData = [
            // 缺少必需的hotelId参数
            'startDate' => '2025-07-09',
            'endDate' => '2025-07-11'
        ];

        $response = $this->postJson('/api/v1/sabre/hotelLowestMemberAndNonMemberPrices', $requestData);

        $response->assertStatus(400)
                ->assertJson([
                    'code' => 400,
                    'message' => '参数验证失败'
                ]);
    }

    /**
     * 测试无效的酒店ID
     */
    public function test_validation_fails_with_invalid_hotel_id()
    {
        $requestData = [
            'hotelId' => 'invalid', // 无效的酒店ID
            'startDate' => '2025-07-09',
            'endDate' => '2025-07-11'
        ];

        $response = $this->postJson('/api/v1/sabre/hotelLowestMemberAndNonMemberPrices', $requestData);

        $response->assertStatus(400)
                ->assertJson([
                    'code' => 400,
                    'message' => '参数验证失败'
                ]);
    }

    /**
     * 测试日期验证失败
     */
    public function test_validation_fails_with_invalid_dates()
    {
        $requestData = [
            'hotelIds' => [100823],
            'startDate' => '2023-01-01', // 过去的日期
            'endDate' => '2023-01-02'
        ];

        $response = $this->postJson('/api/v1/sabre/multipleHotelsLowestMemberAndNonMemberPrices', $requestData);

        $response->assertStatus(400)
                ->assertJson([
                    'code' => 400,
                    'message' => '参数验证失败'
                ]);
    }

    /**
     * 测试儿童年龄验证
     */
    public function test_validation_fails_with_invalid_children_age()
    {
        $requestData = [
            'hotelId' => 100823,
            'startDate' => '2025-07-09',
            'endDate' => '2025-07-11',
            'children' => 2,
            'childrenAge' => '8' // 儿童数量2个，但只提供了1个年龄
        ];

        $response = $this->postJson('/api/v1/sabre/hotelLowestMemberAndNonMemberPrices', $requestData);

        $response->assertStatus(400)
                ->assertJson([
                    'code' => 400,
                    'message' => '儿童数量(2)与提供的年龄数量(1)不匹配'
                ]);
    }

    /**
     * 测试服务异常处理
     */
    public function test_handles_service_exception()
    {
        $requestData = [
            'hotelId' => 100823,
            'startDate' => '2025-07-09',
            'endDate' => '2025-07-11'
        ];

        // 模拟服务抛出异常
        $mockService = $this->createMock(SabreAvailabilityService::class);
        $mockService->method('checkHotelLowestMemberAndNonMemberPrices')
                   ->willThrowException(new \Exception('服务异常'));

        $this->app->instance(SabreAvailabilityService::class, $mockService);

        $response = $this->postJson('/api/v1/sabre/hotelLowestMemberAndNonMemberPrices', $requestData);

        $response->assertStatus(500)
                ->assertJson([
                    'code' => 500,
                    'message' => '系统错误，请稍后再试'
                ]);
    }

    /**
     * 测试酒店无可用房间的情况
     */
    public function test_handles_no_available_rooms()
    {
        $requestData = [
            'hotelId' => 100826,
            'startDate' => '2025-07-09',
            'endDate' => '2025-07-11'
        ];

        // 模拟无可用房间的响应
        $mockService = $this->createMock(SabreAvailabilityService::class);
        $mockService->method('checkHotelLowestMemberAndNonMemberPrices')
                   ->willReturn([
                       'success' => false,
                       'message' => '该酒店在指定日期无可用房间或价格',
                       'hotelId' => 100826,
                       'query_params' => [
                           'hotelId' => 100826,
                           'startDate' => '2025-07-09',
                           'endDate' => '2025-07-11'
                       ]
                   ]);

        $this->app->instance(SabreAvailabilityService::class, $mockService);

        $response = $this->postJson('/api/v1/sabre/hotelLowestMemberAndNonMemberPrices', $requestData);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 200,
                    'message' => 'success',
                    'data' => [
                        'success' => false,
                        'message' => '该酒店在指定日期无可用房间或价格',
                        'hotelId' => 100826
                    ]
                ]);

        $responseData = $response->json('data');
        $this->assertArrayHasKey('query_params', $responseData);
        $this->assertEquals(100826, $responseData['hotelId']);
    }
}
