<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\SabreAvailabilityService;
use App\DTOs\RoomAvailabilityResponseDTO;
use App\DTOs\RoomDTO;
use App\DTOs\RoomRateDTO;

class MultipleHotelsBestPricesTest extends TestCase
{
    /**
     * 测试批量查询多个酒店最佳价格接口
     */
    public function test_multiple_hotels_best_prices_success()
    {
        // 模拟请求数据
        $requestData = [
            'hotelIds' => [100823, 100824, 100825],
            'startDate' => '2025-07-09',
            'endDate' => '2025-07-11',
            'numRooms' => 1,
            'adults' => 2
        ];

        // 发送POST请求
        $response = $this->postJson('/api/v1/sabre/multipleHotelsBestPrices', $requestData);

        // 验证响应状态
        $response->assertStatus(200);

        // 验证响应结构
        $response->assertJsonStructure([
            'code',
            'message',
            'data' => [
                'success',
                'hotels' => [
                    '*' => [
                        'hotelId',
                        'bestPrice' => [
                            'currency',
                            'originalPrice',
                            'cnyPrice',
                            'priceDisplay',
                            'rateCode',
                            'rateName',
                            'isMemberRate',
                            'guaranteePolicy',
                            'cancelRuleString'
                        ],
                        'room' => [
                            'roomCode',
                            'roomName',
                            'roomDescription'
                        ],
                        'availableRoomsCount',
                        'totalRatesCount'
                    ]
                ],
                'total_count',
                'errors',
                'query_params'
            ]
        ]);

        // 验证响应数据
        $data = $response->json('data');
        $this->assertTrue($data['success']);
        $this->assertIsArray($data['hotels']);
        $this->assertIsArray($data['errors']);
        $this->assertIsInt($data['total_count']);
    }

    /**
     * 测试参数验证失败
     */
    public function test_multiple_hotels_best_prices_validation_fails()
    {
        // 缺少必需参数
        $requestData = [
            'startDate' => '2025-07-09',
            'endDate' => '2025-07-11'
        ];

        $response = $this->postJson('/api/v1/sabre/multipleHotelsBestPrices', $requestData);

        $response->assertStatus(400);
        $response->assertJsonStructure([
            'code',
            'message',
            'data' => [
                'hotelIds'
            ]
        ]);
    }

    /**
     * 测试酒店ID数量超限
     */
    public function test_multiple_hotels_best_prices_too_many_hotels()
    {
        // 超过20个酒店ID
        $hotelIds = range(100001, 100025); // 25个酒店ID

        $requestData = [
            'hotelIds' => $hotelIds,
            'startDate' => '2025-07-09',
            'endDate' => '2025-07-11',
            'numRooms' => 1,
            'adults' => 2
        ];

        $response = $this->postJson('/api/v1/sabre/multipleHotelsBestPrices', $requestData);

        $response->assertStatus(400);
        $response->assertJsonPath('message', '参数验证失败');
    }

    /**
     * 测试日期验证
     */
    public function test_multiple_hotels_best_prices_invalid_dates()
    {
        // 入住日期早于今天
        $requestData = [
            'hotelIds' => [100823, 100824],
            'startDate' => '2024-01-01',
            'endDate' => '2024-01-03',
            'numRooms' => 1,
            'adults' => 2
        ];

        $response = $this->postJson('/api/v1/sabre/multipleHotelsBestPrices', $requestData);

        $response->assertStatus(400);
    }

    /**
     * 测试儿童年龄验证
     */
    public function test_multiple_hotels_best_prices_children_age_validation()
    {
        // 儿童数量与年龄不匹配
        $requestData = [
            'hotelIds' => [100823, 100824],
            'startDate' => '2025-07-09',
            'endDate' => '2025-07-11',
            'numRooms' => 1,
            'adults' => 2,
            'children' => 2,
            'childrenAge' => '8' // 只提供了1个年龄，但有2个儿童
        ];

        $response = $this->postJson('/api/v1/sabre/multipleHotelsBestPrices', $requestData);

        $response->assertStatus(400);
    }

    /**
     * 测试带促销代码的查询
     */
    public function test_multiple_hotels_best_prices_with_promo_code()
    {
        $requestData = [
            'hotelIds' => [100823, 100824],
            'startDate' => '2025-07-09',
            'endDate' => '2025-07-11',
            'numRooms' => 1,
            'adults' => 2,
            'accessCode' => 'SUMMER2025'
        ];

        $response = $this->postJson('/api/v1/sabre/multipleHotelsBestPrices', $requestData);

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertEquals('***', $data['query_params']['accessCode']);
    }
}
