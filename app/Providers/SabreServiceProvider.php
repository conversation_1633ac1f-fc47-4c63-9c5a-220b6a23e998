<?php

namespace App\Providers;

use App\Services\SabreService;
use App\Services\SabreAvailabilityService;
use App\Services\SabreReservationService;
use App\Services\SabreHotelService;
use App\Services\SabreDataTransformer;
use App\Services\SabreReservationTransformer;
use Illuminate\Support\ServiceProvider;

class SabreServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // 注册主要的Sabre服务
        $this->app->singleton(SabreService::class, function ($app) {
            return new SabreService();
        });

        // 注册数据转换器
        $this->app->singleton(SabreDataTransformer::class, function ($app) {
            return new SabreDataTransformer();
        });

        // 注册可用性查询服务
        $this->app->singleton(SabreAvailabilityService::class, function ($app) {
            return new SabreAvailabilityService(
                $app->make(SabreService::class),
                $app->make(SabreDataTransformer::class)
            );
        });

        // 注册预订服务
        $this->app->singleton(SabreReservationService::class, function ($app) {
            return new SabreReservationService($app->make(SabreService::class));
        });

        // 注册酒店服务
        $this->app->singleton(SabreHotelService::class, function ($app) {
            return new SabreHotelService($app->make(SabreService::class));
        });

        // 注册预订转换服务
        $this->app->singleton(SabreReservationTransformer::class, function ($app) {
            return new SabreReservationTransformer();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // 发布配置文件
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__.'/../../config/sabre.php' => config_path('sabre.php'),
            ], 'sabre-config');
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            SabreService::class,
            SabreAvailabilityService::class,
            SabreReservationService::class,
            SabreHotelService::class,
            SabreDataTransformer::class,
            SabreReservationTransformer::class,
        ];
    }
}
