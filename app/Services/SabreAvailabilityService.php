<?php

namespace App\Services;

use App\DTOs\Sabre\AvailabilityRequestDTO;
use App\DTOs\RoomAvailabilityResponseDTO;
use App\Exceptions\SabreApiException;
use App\Traits\SabreLoggingTrait;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SabreAvailabilityService
{
    use SabreLoggingTrait;

    protected SabreService $sabreService;
    protected SabreDataTransformer $dataTransformer;

    public function __construct(SabreService $sabreService, SabreDataTransformer $dataTransformer)
    {
        $this->sabreService = $sabreService;
        $this->dataTransformer = $dataTransformer;
    }

    /**
     * 查询酒店可用性（会员和非会员价格，无促销）
     */
    public function checkMemberAndNonMemberRates(array $params): array
    {
        $request = new AvailabilityRequestDTO(
            primaryChannel: $params['primaryChannel'] ?? config('sabre.defaults.primary_channel'),
            secondaryChannel: $params['secondaryChannel'] ?? config('sabre.defaults.secondary_channel'),
            chainId: $params['chainId'] ?? config('sabre.defaults.chain_id'),
            hotelId: $params['hotelId'],
            startDate: $params['startDate'],
            endDate: $params['endDate'],
            numRooms: $params['numRooms'] ?? 1,
            adults: $params['adults'] ?? 1,
            children: $params['children'] ?? null,
            childrenAge: $params['childrenAge'] ?? null,
            loyaltyProgram: $params['loyaltyProgram'] ?? config('sabre.defaults.loyalty_program'),
            loyaltyLevel: $params['loyaltyLevel'] ?? null,
            content: $params['content'] ?? 'full'
        );

        return $this->checkAvailabilityWithCache($request, $params);
    }

    /**
     * 查询酒店可用性并返回标准格式（会员和非会员价格，无促销）
     */
    public function checkMemberAndNonMemberRatesStandard(array $params): RoomAvailabilityResponseDTO
    {
        $sabreResponse = $this->checkMemberAndNonMemberRates($params);
        return $this->dataTransformer->transformAvailabilityResponse($sabreResponse);
    }

    /**
     * 查询酒店可用性（会员和非会员价格，包含促销）
     */
    public function checkMemberAndNonMemberRatesWithPromo(array $params): array
    {
        $request = new AvailabilityRequestDTO(
            primaryChannel: $params['primaryChannel'] ?? config('sabre.defaults.primary_channel'),
            secondaryChannel: $params['secondaryChannel'] ?? config('sabre.defaults.secondary_channel'),
            chainId: $params['chainId'] ?? config('sabre.defaults.chain_id'),
            hotelId: $params['hotelId'],
            startDate: $params['startDate'],
            endDate: $params['endDate'],
            numRooms: $params['numRooms'] ?? 1,
            adults: $params['adults'] ?? 1,
            children: $params['children'] ?? null,
            childrenAge: $params['childrenAge'] ?? null,
            loyaltyProgram: $params['loyaltyProgram'] ?? config('sabre.defaults.loyalty_program'),
            loyaltyLevel: $params['loyaltyLevel'] ?? null,
            content: $params['content'] ?? 'full',
            accessType: $params['accessType'] ?? 'Promotion',
            accessCode: $params['accessCode'] ?? null
        );

        return $this->checkAvailabilityWithCache($request, $params);
    }

    /**
     * 查询酒店可用性并返回标准格式（会员和非会员价格，包含促销）
     */
    public function checkMemberAndNonMemberRatesWithPromoStandard(array $params): RoomAvailabilityResponseDTO
    {
        $sabreResponse = $this->checkMemberAndNonMemberRatesWithPromo($params);
        return $this->dataTransformer->transformAvailabilityResponse($sabreResponse);
    }

    /**
     * 查询酒店可用性（仅促销价格）
     */
    public function checkPromoRateOnly(array $params): array
    {
        $request = new AvailabilityRequestDTO(
            primaryChannel: $params['primaryChannel'] ?? config('sabre.defaults.primary_channel'),
            secondaryChannel: $params['secondaryChannel'] ?? config('sabre.defaults.secondary_channel'),
            chainId: $params['chainId'] ?? config('sabre.defaults.chain_id'),
            hotelId: $params['hotelId'],
            startDate: $params['startDate'],
            endDate: $params['endDate'],
            numRooms: $params['numRooms'] ?? 1,
            adults: $params['adults'] ?? 1,
            children: $params['children'] ?? null,
            childrenAge: $params['childrenAge'] ?? null,
            loyaltyProgram: $params['loyaltyProgram'] ?? config('sabre.defaults.loyalty_program'),
            loyaltyLevel: $params['loyaltyLevel'] ?? null,
            content: $params['content'] ?? 'full',
            onlyCheckRequested: true,
            accessType: $params['accessType'] ?? 'Promotion',
            accessCode: $params['accessCode'] ?? null
        );

        return $this->checkAvailabilityWithCache($request, $params);
    }

    /**
     * 查询酒店可用性并返回标准格式（仅促销价格）
     */
    public function checkPromoRateOnlyStandard(array $params): RoomAvailabilityResponseDTO
    {
        $sabreResponse = $this->checkPromoRateOnly($params);
        return $this->dataTransformer->transformAvailabilityResponse($sabreResponse);
    }

    /**
     * 查询多房间可用性
     */
    public function checkMultiRoomAvailability(array $params): array
    {
        $request = new AvailabilityRequestDTO(
            primaryChannel: $params['primaryChannel'] ?? config('sabre.defaults.primary_channel'),
            secondaryChannel: $params['secondaryChannel'] ?? config('sabre.defaults.secondary_channel'),
            chainId: $params['chainId'] ?? config('sabre.defaults.chain_id'),
            hotelId: $params['hotelId'],
            startDate: $params['startDate'],
            endDate: $params['endDate'],
            numRooms: $params['numRooms'] ?? 2,
            adults: $params['adults'] ?? 2,
            children: $params['children'] ?? null,
            childrenAge: $params['childrenAge'] ?? null,
            loyaltyProgram: $params['loyaltyProgram'] ?? config('sabre.defaults.loyalty_program'),
            loyaltyLevel: $params['loyaltyLevel'] ?? null,
            content: $params['content'] ?? 'full',
            accessType: $params['accessType'] ?? null,
            accessCode: $params['accessCode'] ?? null
        );

        return $this->checkAvailabilityWithCache($request, $params);
    }

    /**
     * 查询多房间可用性并返回标准格式
     */
    public function checkMultiRoomAvailabilityStandard(array $params): RoomAvailabilityResponseDTO
    {
        $sabreResponse = $this->checkMultiRoomAvailability($params);
        return $this->dataTransformer->transformAvailabilityResponse($sabreResponse);
    }

    /**
     * 带缓存的可用性查询
     */
    protected function checkAvailabilityWithCache(AvailabilityRequestDTO $request, array $params): array
    {
        // 生成缓存键
        $cacheKey = $this->generateCacheKey($request);

        // 检查是否启用缓存
        if (config('sabre.cache.enabled', true) && config('sabre.cache.availability_ttl') > 0) {
            $cached = Cache::get($cacheKey);
            if ($cached) {
                if ($this->isSabreLoggingEnabled()) {
                    $this->logSabreCacheHit('availability', $cacheKey, [
                        'hotel_id' => $params['hotelId'] ?? null,
                        'date_range' => ($params['startDate'] ?? '') . ' to ' . ($params['endDate'] ?? ''),
                    ]);
                }
                return $cached;
            } else {
                if ($this->isSabreLoggingEnabled()) {
                    $this->logSabreCacheMiss('availability', $cacheKey, [
                        'hotel_id' => $params['hotelId'] ?? null,
                        'date_range' => ($params['startDate'] ?? '') . ' to ' . ($params['endDate'] ?? ''),
                    ]);
                }
            }
        }

        try {
            $result = $this->sabreService->checkAvailability($request);

            // 缓存结果
            if (config('sabre.cache.enabled', true) && config('sabre.cache.availability_ttl') > 0) {
                Cache::put($cacheKey, $result, config('sabre.cache.availability_ttl'));
            }

            return $result;

        } catch (SabreApiException $e) {
            Log::error('Sabre Availability Service Error', [
                'message' => $e->getMessage(),
                'params' => $params,
                'context' => $e->getContext(),
            ]);

            throw $e;
        }
    }

    /**
     * 生成缓存键
     */
    protected function generateCacheKey(AvailabilityRequestDTO $request): string
    {
        $params = $request->toArray();
        ksort($params);
        return 'sabre_availability_' . md5(json_encode($params));
    }

    /**
     * 解析可用性响应数据
     */
    public function parseAvailabilityResponse(array $response): array
    {
        $parsed = [
            'success' => false,
            'hotels' => [],
            'total_count' => 0,
            'errors' => [],
        ];

        try {
            if (isset($response['productAvailability'])) {
                $parsed['success'] = true;
                $productAvailability = $response['productAvailability'];

                if (isset($productAvailability['Prices']) && is_array($productAvailability['Prices'])) {
                    foreach ($productAvailability['Prices'] as $price) {
                        $parsed['hotels'][] = $this->parseHotelPrice($price);
                    }
                    $parsed['total_count'] = count($productAvailability['Prices']);
                }
            } else {
                $parsed['errors'][] = '未找到可用性数据';
            }

        } catch (\Exception $e) {
            Log::error('Parse Availability Response Error', [
                'message' => $e->getMessage(),
                'response' => $response,
            ]);

            $parsed['errors'][] = '解析响应数据失败: ' . $e->getMessage();
        }

        return $parsed;
    }

    /**
     * 解析酒店价格数据
     */
    protected function parseHotelPrice(array $priceData): array
    {
        return [
            'product' => $priceData['Product'] ?? [],
            'price' => $priceData['Price'] ?? [],
            'availability' => $priceData['Availability'] ?? [],
            'policies' => $priceData['Policies'] ?? [],
            'amenities' => $priceData['Amenities'] ?? [],
            'images' => $priceData['Images'] ?? [],
            'description' => $priceData['Description'] ?? '',
        ];
    }

    /**
     * 批量查询多个酒店的最佳价格
     */
    public function checkMultipleHotelsBestPrices(array $params): array
    {
        $hotelIds = $params['hotelIds'] ?? [];
        if (empty($hotelIds)) {
            throw new \InvalidArgumentException('酒店ID列表不能为空');
        }

        if (count($hotelIds) > 20) {
            throw new \InvalidArgumentException('一次最多只能查询20个酒店');
        }

        $results = [];
        $errors = [];

        // 并发查询多个酒店
        foreach ($hotelIds as $hotelId) {
            try {
                $hotelParams = array_merge($params, ['hotelId' => $hotelId]);

                // 根据参数选择查询方法
                if (!empty($params['accessCode'])) {
                    $hotelResult = $this->checkMemberAndNonMemberRatesWithPromoStandard($hotelParams);
                } else {
                    $hotelResult = $this->checkMemberAndNonMemberRatesStandard($hotelParams);
                }

                // 提取最佳价格
                $bestPrice = $this->extractBestPriceFromResult($hotelResult, $hotelId);
                if ($bestPrice) {
                    $results[] = $bestPrice;
                }

            } catch (\Exception $e) {
                $errors[] = [
                    'hotelId' => $hotelId,
                    'error' => $e->getMessage()
                ];

                if ($this->isSabreLoggingEnabled()) {
                    Log::warning('批量查询酒店价格失败', [
                        'hotel_id' => $hotelId,
                        'error' => $e->getMessage(),
                        'params' => $hotelParams
                    ]);
                }
            }
        }

        return [
            'success' => true,
            'hotels' => $results,
            'total_count' => count($results),
            'errors' => $errors,
            'query_params' => [
                'startDate' => $params['startDate'] ?? null,
                'endDate' => $params['endDate'] ?? null,
                'numRooms' => $params['numRooms'] ?? 1,
                'adults' => $params['adults'] ?? 1,
                'children' => $params['children'] ?? 0,
                'accessCode' => !empty($params['accessCode']) ? '***' : null
            ]
        ];
    }

    /**
     * 从查询结果中提取最佳价格信息
     */
    protected function extractBestPriceFromResult($result, int $hotelId): ?array
    {
        if (!$result || !method_exists($result, 'toArray')) {
            return null;
        }

        $resultArray = $result->toArray();
        $rooms = $resultArray['rooms'] ?? [];

        if (empty($rooms)) {
            return null;
        }

        $bestPrice = null;
        $bestRate = null;
        $roomInfo = null;

        // 遍历所有房型和价格，找到最低价格
        foreach ($rooms as $room) {
            $roomRates = $room['roomRateList'] ?? [];

            foreach ($roomRates as $rate) {
                $cnyTotalPrice = $rate['cny_total_price'] ?? 0;

                if ($cnyTotalPrice > 0 && ($bestPrice === null || $cnyTotalPrice < $bestPrice)) {
                    $bestPrice = $cnyTotalPrice;
                    $bestRate = $rate;
                    $roomInfo = [
                        'roomCode' => $room['roomCode'] ?? '',
                        'roomName' => $room['roomName'] ?? '',
                        'roomDescription' => $room['roomDescription'] ?? ''
                    ];
                }
            }
        }

        if ($bestPrice === null || $bestRate === null) {
            return null;
        }

        return [
            'hotelId' => $hotelId,
            'bestPrice' => [
                'currency' => $bestRate['currency'] ?? 'USD',
                'originalPrice' => $bestRate['total_price'] ?? 0,
                'cnyPrice' => $bestPrice,
                'priceDisplay' => '¥' . number_format($bestPrice / 100, 2),
                'rateCode' => $bestRate['rateCode'] ?? '',
                'rateName' => $bestRate['rateName'] ?? '',
                'isMemberRate' => $bestRate['isMemberRate'] ?? false,
                'guaranteePolicy' => $bestRate['guaranteePolicy'] ?? '',
                'cancelRuleString' => $bestRate['cancelRuleString'] ?? ''
            ],
            'room' => $roomInfo,
            'availableRoomsCount' => count($rooms),
            'totalRatesCount' => array_sum(array_map(function($room) {
                return count($room['roomRateList'] ?? []);
            }, $rooms))
        ];
    }

    /**
     * 查询单个酒店的最低会员价和对应非会员价
     */
    public function checkHotelLowestMemberAndNonMemberPrices(array $params): array
    {
        $hotelId = $params['hotelId'] ?? null;

        if (empty($hotelId)) {
            throw new \InvalidArgumentException('酒店ID不能为空');
        }

        try {
            // 根据参数选择查询方法
            if (!empty($params['accessCode'])) {
                $hotelResult = $this->checkMemberAndNonMemberRatesWithPromoStandard($params);
            } else {
                $hotelResult = $this->checkMemberAndNonMemberRatesStandard($params);
            }

            // 提取最低会员价和对应非会员价
            $priceComparison = $this->extractLowestMemberAndNonMemberPrices($hotelResult, $hotelId);

            if (!$priceComparison) {
                return [
                    'success' => false,
                    'message' => '该酒店在指定日期无可用房间或价格',
                    'hotelId' => $hotelId,
                    'query_params' => [
                        'hotelId' => $hotelId,
                        'startDate' => $params['startDate'] ?? null,
                        'endDate' => $params['endDate'] ?? null,
                        'numRooms' => $params['numRooms'] ?? 1,
                        'adults' => $params['adults'] ?? 1,
                        'children' => $params['children'] ?? 0,
                        'accessCode' => !empty($params['accessCode']) ? '***' : null
                    ]
                ];
            }

            return [
                'success' => true,
                'hotel' => $priceComparison,
                'query_params' => [
                    'hotelId' => $hotelId,
                    'startDate' => $params['startDate'] ?? null,
                    'endDate' => $params['endDate'] ?? null,
                    'numRooms' => $params['numRooms'] ?? 1,
                    'adults' => $params['adults'] ?? 1,
                    'children' => $params['children'] ?? 0,
                    'accessCode' => !empty($params['accessCode']) ? '***' : null
                ]
            ];

        } catch (\Exception $e) {
            if ($this->isSabreLoggingEnabled()) {
                Log::warning('查询酒店会员价和非会员价失败', [
                    'hotel_id' => $hotelId,
                    'error' => $e->getMessage(),
                    'params' => $params
                ]);
            }

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'hotelId' => $hotelId,
                'query_params' => [
                    'hotelId' => $hotelId,
                    'startDate' => $params['startDate'] ?? null,
                    'endDate' => $params['endDate'] ?? null,
                    'numRooms' => $params['numRooms'] ?? 1,
                    'adults' => $params['adults'] ?? 1,
                    'children' => $params['children'] ?? 0,
                    'accessCode' => !empty($params['accessCode']) ? '***' : null
                ]
            ];
        }
    }

    /**
     * 从查询结果中提取最低会员价和对应非会员价
     */
    protected function extractLowestMemberAndNonMemberPrices($result, int $hotelId): ?array
    {
        if (!$result || !method_exists($result, 'toArray')) {
            return null;
        }

        $resultArray = $result->toArray();
        $rooms = $resultArray['rooms'] ?? [];

        if (empty($rooms)) {
            return null;
        }

        $lowestMemberPrice = null;
        $lowestMemberRate = null;
        $lowestNonMemberPrice = null;
        $lowestNonMemberRate = null;
        $memberRoomInfo = null;
        $nonMemberRoomInfo = null;

        // 遍历所有房型和价格，分别找到最低会员价和最低非会员价
        foreach ($rooms as $room) {
            $roomRates = $room['roomRateList'] ?? [];

            foreach ($roomRates as $rate) {
                $cnyTotalPrice = $rate['cny_total_price'] ?? 0;
                $isMemberRate = $rate['isMemberRate'] ?? false;

                if ($cnyTotalPrice <= 0) {
                    continue;
                }

                // 处理会员价
                if ($isMemberRate && ($lowestMemberPrice === null || $cnyTotalPrice < $lowestMemberPrice)) {
                    $lowestMemberPrice = $cnyTotalPrice;
                    $lowestMemberRate = $rate;
                    $memberRoomInfo = [
                        'roomCode' => $room['roomCode'] ?? '',
                        'roomName' => $room['roomName'] ?? '',
                        'roomDescription' => $room['roomDescription'] ?? ''
                    ];
                }

                // 处理非会员价
                if (!$isMemberRate && ($lowestNonMemberPrice === null || $cnyTotalPrice < $lowestNonMemberPrice)) {
                    $lowestNonMemberPrice = $cnyTotalPrice;
                    $lowestNonMemberRate = $rate;
                    $nonMemberRoomInfo = [
                        'roomCode' => $room['roomCode'] ?? '',
                        'roomName' => $room['roomName'] ?? '',
                        'roomDescription' => $room['roomDescription'] ?? ''
                    ];
                }
            }
        }

        // 如果没有找到会员价或非会员价，返回null
        if ($lowestMemberPrice === null && $lowestNonMemberPrice === null) {
            return null;
        }

        $result = [
            'hotelId' => $hotelId,
            'availableRoomsCount' => count($rooms),
            'totalRatesCount' => array_sum(array_map(function($room) {
                return count($room['roomRateList'] ?? []);
            }, $rooms))
        ];

        // 添加会员价信息
        if ($lowestMemberPrice !== null && $lowestMemberRate !== null) {
            $result['memberPrice'] = [
                'currency' => $lowestMemberRate['currency'] ?? 'USD',
                'originalPrice' => $lowestMemberRate['total_price'] ?? 0,
                'cnyPrice' => $lowestMemberPrice,
                'priceDisplay' => '¥' . number_format($lowestMemberPrice / 100, 2),
                'rateCode' => $lowestMemberRate['rateCode'] ?? '',
                'rateName' => $lowestMemberRate['rateName'] ?? '',
                'guaranteePolicy' => $lowestMemberRate['guaranteePolicy'] ?? '',
                'cancelRuleString' => $lowestMemberRate['cancelRuleString'] ?? ''
            ];
            $result['memberRoom'] = $memberRoomInfo;
        }

        // 添加非会员价信息
        if ($lowestNonMemberPrice !== null && $lowestNonMemberRate !== null) {
            $result['nonMemberPrice'] = [
                'currency' => $lowestNonMemberRate['currency'] ?? 'USD',
                'originalPrice' => $lowestNonMemberRate['total_price'] ?? 0,
                'cnyPrice' => $lowestNonMemberPrice,
                'priceDisplay' => '¥' . number_format($lowestNonMemberPrice / 100, 2),
                'rateCode' => $lowestNonMemberRate['rateCode'] ?? '',
                'rateName' => $lowestNonMemberRate['rateName'] ?? '',
                'guaranteePolicy' => $lowestNonMemberRate['guaranteePolicy'] ?? '',
                'cancelRuleString' => $lowestNonMemberRate['cancelRuleString'] ?? ''
            ];
            $result['nonMemberRoom'] = $nonMemberRoomInfo;
        }

        // 计算价格差异
        if ($lowestMemberPrice !== null && $lowestNonMemberPrice !== null) {
            $priceDifference = $lowestNonMemberPrice - $lowestMemberPrice;
            $discountPercentage = $lowestNonMemberPrice > 0 ? round(($priceDifference / $lowestNonMemberPrice) * 100, 2) : 0;

            $result['priceComparison'] = [
                'memberSavings' => $priceDifference,
                'memberSavingsDisplay' => '¥' . number_format($priceDifference / 100, 2),
                'discountPercentage' => $discountPercentage,
                'discountPercentageDisplay' => $discountPercentage . '%'
            ];
        }

        return $result;
    }

    /**
     * 清除可用性缓存
     */
    public function clearAvailabilityCache(array $params = []): bool
    {
        if (empty($params)) {
            // 清除所有可用性缓存
            return Cache::flush();
        }

        // 清除特定参数的缓存
        $request = AvailabilityRequestDTO::fromArray($params);
        $cacheKey = $this->generateCacheKey($request);

        return Cache::forget($cacheKey);
    }
}
