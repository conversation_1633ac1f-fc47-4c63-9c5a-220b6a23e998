<?php

namespace App\Services;

use App\DTOs\Sabre\AuthenticationRequestDTO;
use App\DTOs\Sabre\AuthenticationResponseDTO;
use App\DTOs\Sabre\AvailabilityRequestDTO;
use App\DTOs\Sabre\ReservationRequestDTO;
use App\Exceptions\SabreApiException;
use App\Traits\SabreLoggingTrait;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SabreService
{
    use SabreLoggingTrait;

    protected Client $client;
    protected string $authUrl;
    protected string $apiUrl;
    protected string $username;
    protected string $password;
    protected string $apiKey;

    public function __construct()
    {
        $this->client = new Client([
            'headers' => [
                'Accept'       => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'verify'  => false,
            'timeout' => 30,
        ]);

        $this->authUrl  = config('sabre.auth_url');
        $this->apiUrl   = config('sabre.api_url');
        $this->username = config('sabre.username');
        $this->password = config('sabre.password');
        $this->apiKey   = config('sabre.api_key');
    }

    /**
     * 获取认证令牌
     */
    public function authenticate(): AuthenticationResponseDTO
    {
        $endpoint = 'auth/token';

        // 检查是否启用缓存以及缓存中是否有有效的令牌
        $cachedToken = Cache::get('sabre_auth_token');
        if ($cachedToken) {
            if ($this->isSabreLoggingEnabled()) {
                $this->logSabreCacheHit($endpoint, 'sabre_auth_token', [
                    'token_type' => $cachedToken['tokenType'] ?? 'unknown',
                    'expires_in' => $cachedToken['expiresIn'] ?? 0,
                ]);
            }
            return AuthenticationResponseDTO::fromArray($cachedToken);
        } else {
            if ($this->isSabreLoggingEnabled()) {
                $this->logSabreCacheMiss($endpoint, 'sabre_auth_token');
            }
        }


        $startTime = microtime(true);
        $requestId = '';

        try {
            $authRequest = new AuthenticationRequestDTO($this->username, $this->password);
            $requestData = $authRequest->toArray();
            $headers     = [
                'X-API-KEY'    => $this->apiKey,
                'Accept'       => 'application/json',
                'Content-Type' => 'application/json',
            ];

            // 记录请求日志
            if ($this->isSabreLoggingEnabled()) {
                $requestId = $this->logSabreRequest($this->authUrl, $requestData, $headers, 'POST');
            }

            $response = $this->client->post($this->authUrl, [
                'headers' => $headers,
                'json'    => $requestData,
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);
            $duration     = microtime(true) - $startTime;

            // 记录响应日志
            if ($this->isSabreLoggingEnabled()) {
                $this->logSabreResponse($requestId, $endpoint, $responseData, $response->getStatusCode(), $duration);
            }

            $authResponse = AuthenticationResponseDTO::fromArray($responseData);

            if ($authResponse->isSuccess() && config('sabre.cache.enabled', true)) {
                // 缓存令牌，设置过期时间比实际过期时间少5分钟
                $cacheTime = ($authResponse->expiresIn ?? 3600) - 300;
                Cache::put('sabre_auth_token', $authResponse->toArray(), $cacheTime);
            }

            return $authResponse;

        } catch (GuzzleException $e) {
            $duration = microtime(true) - $startTime;

            // 记录错误日志
            if ($this->isSabreLoggingEnabled()) {
                $this->logSabreError($requestId, $endpoint, $e, [
                    'auth_url'    => $this->authUrl,
                    'duration_ms' => round($duration * 1000, 2),
                ]);
            }

            throw new SabreApiException('认证失败: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * 获取有效的认证令牌
     */
    public function getValidToken(): string
    {
        $authResponse = $this->authenticate();

        if (!$authResponse->isSuccess()) {
            throw new SabreApiException('无法获取有效的认证令牌: ' . $authResponse->error);
        }
        return $authResponse->token;
    }

    /**
     * 查询酒店可用性
     */
    public function checkAvailability(AvailabilityRequestDTO $request): array
    {
        $endpoint  = 'hotel/availability';
        $startTime = microtime(true);
        $requestId = '';

        try {
            $token = $this->getValidToken();

            $url         = $this->apiUrl . '/v1/api/hotel/availability';
            $queryParams = $request->toArray();
            $headers     = [
                'Authorization'   => 'Bearer ' . $token,
                'Context'         => 'WBSVC',
                'Accept-Language' => 'zh-CN',
                'Accept'          => 'application/json',
            ];

            // 记录请求日志
            if ($this->isSabreLoggingEnabled()) {
                $requestId = $this->logSabreRequest($url, $queryParams, $headers, 'GET');
            }
            $response = $this->client->get($url, [
                'headers' => $headers,
                'query'   => $queryParams,
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);
            $duration     = microtime(true) - $startTime;

            // 记录响应日志
            if ($this->isSabreLoggingEnabled()) {
                $this->logSabreResponse($requestId, $endpoint, $responseData, $response->getStatusCode(), $duration);

                // 记录性能指标
                $this->logSabrePerformance($endpoint, $duration, strlen(json_encode($responseData)), [
                    'hotel_id'   => $queryParams['hotelId'] ?? null,
                    'num_rooms'  => $queryParams['numRooms'] ?? null,
                    'date_range' => ($queryParams['startDate'] ?? '') . ' to ' . ($queryParams['endDate'] ?? ''),
                ]);
            }

            return $responseData;

        } catch (GuzzleException $e) {
            $duration = microtime(true) - $startTime;

            // 记录错误日志
            if ($this->isSabreLoggingEnabled()) {
                $this->logSabreError($requestId, $endpoint, $e, [
                    'request_params' => $request->toArray(),
                    'api_url'        => $url,
                    'duration_ms'    => round($duration * 1000, 2),
                ]);
            }

            throw new SabreApiException('查询酒店可用性失败: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * 创建预订
     */
    public function createReservation(ReservationRequestDTO $request): array
    {
        $endpoint  = 'reservation/create';
        $startTime = microtime(true);
        $requestId = '';

        try {
            $token = $this->getValidToken();

            $url         = $this->apiUrl . '/v1/api/reservation';
            $requestData = $request->toArray();
            $headers     = [
                'Authorization' => 'Bearer ' . $token,
                'Context'       => 'WBSVC',
                'Accept'        => 'application/json',
                'Content-Type'  => 'application/json',
            ];
            // 记录请求日志
            if ($this->isSabreLoggingEnabled()) {
                $requestId = $this->logSabreRequest($url, $requestData, $headers, 'POST');
            }
            $response = $this->client->post($url, [
                'headers' => $headers,
                'json'    => $requestData,
            ]);
            $responseData = json_decode($response->getBody()->getContents(), true);
            $duration     = microtime(true) - $startTime;

            // 记录响应日志
            if ($this->isSabreLoggingEnabled()) {
                if (empty($responseData)){
                    $responseData = [
                        'error' => '未知错误',
                    ];
                }
                $this->logSabreResponse($requestId, $endpoint, $responseData, $response->getStatusCode(), $duration);

                // 记录性能指标
                $this->logSabrePerformance($endpoint, $duration, strlen(json_encode($responseData)), [
                    'hotel_id'    => $requestData['Hotel']['Id'] ?? null,
                    'guest_count' => count($requestData['Guests'] ?? []),
                    'room_count'  => $requestData['RoomStay']['NumRooms'] ?? null,
                ]);
            }

            return $responseData;

        } catch (GuzzleException $e) {
            $duration = microtime(true) - $startTime;

            // 记录错误日志
            if ($this->isSabreLoggingEnabled()) {
                $this->logSabreError($requestId, $endpoint, $e, [
                    'request_data' => $request->toArray(),
                    'api_url'      => $url,
                    'duration_ms'  => round($duration * 1000, 2),
                ]);
            }

            throw new SabreApiException('创建预订失败: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * 根据行程号查询预订
     */
    public function getReservationByItinerary(int $itineraryNumber, string $channel = 'DSCVRYLYLTY'): array
    {
        $endpoint  = 'reservation/get_by_itinerary';
        $startTime = microtime(true);
        $requestId = '';

        try {
            $token = $this->getValidToken();

            $url         = $this->apiUrl . '/v1/api/reservation';
            $queryParams = [
                'view'            => 'Full',
                'channel'         => $channel,
                'ItineraryNumber' => $itineraryNumber,
            ];
            $headers     = [
                'Authorization' => 'Bearer ' . $token,
                'Context'       => 'WBSVC',
                'Accept'        => 'application/json',
            ];

            // 记录请求日志
            if ($this->isSabreLoggingEnabled()) {
                $requestId = $this->logSabreRequest($url, $queryParams, $headers, 'GET');
            }

            $response = $this->client->get($url, [
                'headers' => $headers,
                'query'   => $queryParams,
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);
            $duration     = microtime(true) - $startTime;

            // 记录响应日志
            if ($this->isSabreLoggingEnabled()) {
                $this->logSabreResponse($requestId, $endpoint, $responseData, $response->getStatusCode(), $duration);
            }

            return $responseData;

        } catch (GuzzleException $e) {
            $duration = microtime(true) - $startTime;

            // 记录错误日志
            if ($this->isSabreLoggingEnabled()) {
                $this->logSabreError($requestId, $endpoint, $e, [
                    'itinerary_number' => $itineraryNumber,
                    'channel'          => $channel,
                    'duration_ms'      => round($duration * 1000, 2),
                ]);
            }

            throw new SabreApiException('查询预订失败: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * 根据确认号查询预订
     */
    public function getReservationByConfirmation(
        string $confirmationNumber,
        int    $chainId,
        int    $hotelId,
        string $channel = 'DSCVRYLYLTY'
    ): array {
        try {
            $token = $this->getValidToken();

            $url = $this->apiUrl . '/v1/api/reservation';

            $response = $this->client->get($url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Context'       => 'WBSVC',
                ],
                'query'   => [
                    'view'                  => 'Full',
                    'chain'                 => $chainId,
                    'crsConfirmationNumber' => $confirmationNumber,
                    'hotel'                 => $hotelId,
                    'channel'               => $channel,
                ],
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            Log::info('Sabre Get Reservation by Confirmation Response', [
                'confirmationNumber' => $confirmationNumber,
                'response'           => $responseData
            ]);

            return $responseData;

        } catch (GuzzleException $e) {
            Log::error('Sabre Get Reservation by Confirmation Error', [
                'message'            => $e->getMessage(),
                'code'               => $e->getCode(),
                'confirmationNumber' => $confirmationNumber,
            ]);

            throw new SabreApiException('查询预订失败: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * 修改预订
     */
    public function modifyReservation(array $reservationData): array
    {
        try {
            $token = $this->getValidToken();

            $url = $this->apiUrl . '/v1/api/reservation';

            $response = $this->client->patch($url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Context'       => 'WBSVC',
                ],
                'json'    => $reservationData,
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            Log::info('Sabre Modify Reservation Response', [
                'request'  => $reservationData,
                'response' => $responseData
            ]);

            return $responseData;

        } catch (GuzzleException $e) {
            Log::error('Sabre Modify Reservation Error', [
                'message' => $e->getMessage(),
                'code'    => $e->getCode(),
                'request' => $reservationData,
            ]);

            throw new SabreApiException('修改预订失败: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * 取消预订
     */
    public function cancelReservation(string $confirmationNumber, int $hotelId, string $hotelCode = null): array
    {
        try {
            $token = $this->getValidToken();

            $url = $this->apiUrl . '/v1/api/reservation/cancel';

            $requestData = [
                'Hotel'                 => [
                    'Id' => (string)$hotelId,
                ],
                'CrsConfirmationNumber' => $confirmationNumber,
                'Channels'              => [
                    'PrimaryChannel'   => [
                        'Code' => 'SYDC',
                    ],
                    'SecondaryChannel' => [
                        'Code' => 'DSCVRYLYLTY',
                    ],
                ],
            ];

            if ($hotelCode) {
                $requestData['Hotel']['Code'] = $hotelCode;
            }

            $response = $this->client->post($url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Context'       => 'WBSVC',
                ],
                'json'    => $requestData,
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            Log::info('Sabre Cancel Reservation Response', [
                'request'  => $requestData,
                'response' => $responseData
            ]);

            return $responseData;

        } catch (GuzzleException $e) {
            Log::error('Sabre Cancel Reservation Error', [
                'message'            => $e->getMessage(),
                'code'               => $e->getCode(),
                'confirmationNumber' => $confirmationNumber,
            ]);

            throw new SabreApiException('取消预订失败: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }
}
