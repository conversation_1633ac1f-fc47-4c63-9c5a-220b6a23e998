<?php

namespace App\Http\Controllers\V1;

use App\Models\Room;
use App\Services\SabreAvailabilityService;
use App\Services\SabreHotelService;
use App\Services\SabreReservationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class RoomController extends BaseController
{
    protected SabreAvailabilityService $availabilityService;
    protected SabreReservationService $reservationService;
    protected SabreHotelService $hotelService;

    public function __construct(
        SabreAvailabilityService $availabilityService,
        SabreReservationService $reservationService,
        SabreHotelService $hotelService
    ) {
        $this->availabilityService = $availabilityService;
        $this->reservationService = $reservationService;
        $this->hotelService = $hotelService;
    }

    /**
     * 查询酒店 房间可用性
     * @param Request $request
     * @return JsonResponse
     */
    public function checkAvailability(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'hotelId' => 'required|integer',
            'startDate' => 'required|date|after:today',
            'endDate' => 'required|date|after:startDate',
            'numRooms' => 'integer|min:1|max:10',
            'adults' => 'integer|min:1|max:20',
            'children' => 'integer|min:0|max:10',
            'childrenAge' => 'nullable|string|regex:/^(\d{1,2})(,\d{1,2})*(;\d{1,2}(,\d{1,2})*)*$/',
            'accessCode' => 'nullable|string',
            'onlyPromo' => 'boolean',
        ]);

        if ($validator->fails()) {
            return errorResponse('参数验证失败', 400, $validator->errors());
        }
        $params = $request->all();
        $params['accessType']='Promotion';//Promotion,Group,Corporate
        $params['loyaltyProgram']='GHA';
        $params['loyaltyLevel']='RED';
        $params['format']='standard';

        if (AuthCheck()){
            $params['format']='standard';
        }

        // 验证儿童年龄与儿童数量是否匹配
        if ($request->has('children') && $request->has('childrenAge')) {
            $childrenCount = $request->input('children', 0);
            $childrenAge = $request->input('childrenAge', '');

            if ($childrenCount > 0 && !empty($childrenAge)) {
                $ageValidation = validateChildrenAge($childrenCount, $childrenAge, $request->input('numRooms', 1));
                if (!$ageValidation['valid']) {
                    return errorResponse($ageValidation['message']);
                }
            }
        }
        try {
            // 返回标准格式
            if ($request->boolean('onlyPromo') && !empty($params['accessCode'])) {
                $result = $this->availabilityService->checkPromoRateOnlyStandard($params);
            } elseif (!empty($params['accessCode'])) {
                $result = $this->availabilityService->checkMemberAndNonMemberRatesWithPromoStandard($params);
            } elseif (($params['numRooms'] ?? 1) > 1) {
                $result = $this->availabilityService->checkMultiRoomAvailabilityStandard($params);
            } else {
                $result = $this->availabilityService->checkMemberAndNonMemberRatesStandard($params);
            }
            $rooms=$result->toArray();
            foreach ($rooms['rooms'] as $key => &$value) {
                //获取房型相关信息
                $room=Room::getRoomCode($value['roomCode']);
                $value['room_name']=$room->room_name??$value['roomName'];
                $value['room_desc']=$room->room_desc??$value['roomDescription'];
                $value['room_images']=$room->images??[];
                $value['room_size']=$room->size??'';
                $value['facility']=[
                    ['name'=>'免费WiFi','icon'=>'https://cms.ghadiscovery.com/content/download/565/2567?version=2&inline=1'],
                    ['name'=>'免费停车','icon'=>'https://cms.ghadiscovery.com/content/download/565/2567?version=2&inline=1'],
                    ['name'=>'免费早餐','icon'=>'https://cms.ghadiscovery.com/content/download/565/2567?version=2&inline=1'],
                    ['name'=>'免费接机','icon'=>'https://cms.ghadiscovery.com/content/download/565/2567?version=2&inline=1'],
                ];
            }
        }catch (\Exception $e){
            return errorResponse('查询失败');
        }
        return successResponse($rooms);
    }
}
