<?php

namespace App\DTOs\Sabre;

use Illuminate\Support\Arr;

class LoyaltyMembershipDTO
{
    public function __construct(
        public readonly LoyaltyLevelDTO $level,
        public readonly string $membershipID,
        public readonly string $source,
        public readonly string $programID
    ) {}

    public function toArray(): array
    {
        return [
            'Level' => $this->level->toArray(),
            'MembershipID' => $this->membershipID,
            'Source' => $this->source,
            'ProgramID' => $this->programID,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            level: LoyaltyLevelDTO::fromArray(Arr::get($data, 'Level')),
            membershipID: Arr::get($data, 'MembershipID'),
            source: Arr::get($data, 'Source'),
            programID: Arr::get($data, 'ProgramID')
        );
    }
}


