<?php

namespace App\DTOs\Sabre;

class RoomStayDTO
{
    public function __construct(
        public readonly string $startDate,
        public readonly string $endDate,
        public readonly array $guestCount,
        public readonly int $numRooms,
        public readonly array $products
    ) {}

    public function toArray(): array
    {
        return [
            'StartDate' => $this->startDate,
            'EndDate' => $this->endDate,
            'GuestCount' => $this->guestCount,
            'NumRooms' => $this->numRooms,
            'Products' => $this->products,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            startDate: $data['StartDate'],
            endDate: $data['EndDate'],
            guestCount: $data['GuestCount'],
            numRooms: $data['NumRooms'],
            products: $data['Products']
        );
    }
}


