<?php

namespace App\DTOs\Sabre;

class GuestDTO extends BaseSabreDTO
{
    public function __construct(
        public readonly PersonNameDTO $personName,
        public readonly array $emailAddress,
        public readonly array $contactNumbers,
        public readonly array $locations,
        public readonly array $payments,
        public readonly string $role = 'Primary',
        public readonly ?string $comments = null,
        public readonly ?string $startDate = null,
        public readonly ?string $endDate = null
    ) {}

    public function toArray(): array
    {
        $data = [
            'PersonName' => $this->personName->toArray(),
            'EmailAddress' => $this->emailAddress,
            'ContactNumbers' => $this->contactNumbers,
            'Locations' => $this->locations,
            'Payments' => $this->payments,
            'Role' => $this->role,
        ];

        if ($this->comments !== null) {
            $data['Comments'] = $this->comments;
        }

        if ($this->startDate !== null) {
            $data['StartDate'] = $this->startDate;
        }

        if ($this->endDate !== null) {
            $data['EndDate'] = $this->endDate;
        }

        return $data;
    }

    public static function fromArray(array $data): self
    {
        return new self(
            personName: PersonNameDTO::fromArray(self::toArrayType(self::getField($data, 'PersonName', []))),
            emailAddress: self::toArrayType(self::getField($data, 'EmailAddress', [])),
            contactNumbers: self::toArrayType(self::getField($data, 'ContactNumbers', [])),
            locations: self::toArrayType(self::getField($data, 'Locations', [])),
            payments: self::toArrayType(self::getField($data, 'Payments', [])),
            role: self::toString(self::getField($data, 'Role', 'Primary')),
            comments: self::getField($data, 'Comments'),
            startDate: self::toString(self::getField($data, 'StartDate')),
            endDate: self::toString(self::getField($data, 'EndDate'))
        );
    }
}
