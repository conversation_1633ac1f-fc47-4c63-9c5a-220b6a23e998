<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ElasticsearchService;

class EsCreateHotelIndex extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'es:create-hotel-index';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建hotel索引';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $client = ElasticsearchService::client();

        // 如果存在先删除
        if ($client->indices()->exists(['index' => '.hotel'])) {
            $client->indices()->delete(['index' => '.hotel']);
        }

        $params = [
            'index' => '.hotel',
            'body' => [
                'settings' => [
                    'number_of_shards' => 3,
                    'number_of_replicas' => 1,
                ],
                'mappings' => [
                    'doc' => [
                        'properties' => [
                            'id' => ['type' => 'integer'],
                            'hotelcode' => ['type' => 'keyword'],
                            'hotel_name' => ['type' => 'text'],
                            'brand_id' => ['type' => 'keyword'],
                            'brand_code' => ['type' => 'keyword'],
                            'brand_name' => ['type' => 'keyword'],
                            'brand_img' => ['type' => 'text'],
                            'brand_nameEn' => ['type' => 'text'],
                            'gradient_color' => ['type' => 'keyword'],
                            'latitude' => ['type' => 'float'],
                            'longitude' => ['type' => 'float'],
                            'hotel_type'=> [
                                "type" => "nested",
                                "properties" => [
                                    "id" => ["type" => "integer"],
                                    "name" => ["type" => "keyword"],
                                    "name_en" => ["type" => "keyword"]
                                ]
                            ],
                            'city_code' => [
                                "type" => "nested",
                                "properties" => [
                                    "id" => ["type" => "integer"],
                                    "name" => ["type" => "keyword"],
                                    "name_en" => ["type" => "keyword"]
                                ]
                            ],
                            'country_code' => ['type' => 'keyword'],
                            'created_at' => ['type' => 'date', 'format' => 'yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis'],
                            'new_hotel' => ['type' => 'text'],
                            'images' => ['type' => 'keyword'],
                            'destinations' => [
                                "type" => "nested",
                                "properties" => [
                                    "id" => ["type" => "integer"],
                                    "name" => ["type" => "keyword"],
                                    "name_en" => ["type" => "keyword"]
                                ]
                            ],
                            'interests' => [
                                "type" => "nested",
                                "properties" => [
                                    "id" => ["type" => "integer"],
                                    "name" => ["type" => "keyword"],
                                    "name_en" => ["type" => "keyword"]
                                ]
                            ],
                            'eco_certificates' => [
                                "type" => "nested",
                                "properties" => [
                                    "id" => ["type" => "integer"],
                                    "name" => ["type" => "keyword"],
                                    "name_en" => ["type" => "keyword"]
                                ]
                            ],
                            'categories' => [
                                "type" => "nested",
                                "properties" => [
                                    "id" => ["type" => "integer"],
                                    "name" => ["type" => "keyword"],
                                    "name_en" => ["type" => "keyword"]
                                ]
                            ],
                            'neighborhood_tag' => [
                                "type" => "nested",
                                "properties" => [
                                    "id" => ["type" => "integer"],
                                    "name" => ["type" => "keyword"],
                                    "name_en" => ["type" => "keyword"]
                                ]
                            ],
                            'condition_code' => ['type' => 'keyword'],
                            'new_hotel_start_date' => ['type' => 'integer'],
                            'new_hotel_end_date' => ['type' => 'integer'],
                            'synxis_hotel_id' => ['type' => 'keyword'],
                            'synxis_chain_id' => ['type' => 'keyword'],
                            'feature' => [
                                "type" => "nested",
                                "properties" => [
                                    "id" => ["type" => "integer"],
                                    "name" => ["type" => "keyword"],
                                    "name_en" => ["type" => "keyword"]
                                ]
                            ],
                        ]
                    ]
                ]
            ]
        ];
        $client->indices()->create($params);
        $this->info("索引创建完成");
    }
}
