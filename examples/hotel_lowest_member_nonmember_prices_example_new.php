<?php

/**
 * 查询单个酒店最低会员价和非会员价接口示例
 *
 * 该示例展示如何调用接口获取单个酒店的最低会员价和对应的非会员价
 */

// 模拟请求数据
$requestData = [
    "hotelId" => 100823,
    "startDate" => "2025-07-09",
    "endDate" => "2025-07-11",
    "numRooms" => 1,
    "adults" => 2,
    "children" => 1,
    "childrenAge" => "8",
    "accessCode" => "SUMMER2025"
];

// 模拟API调用
function callHotelLowestMemberAndNonMemberPricesAPI($data) {
    // 这里应该是实际的HTTP请求
    // 为了演示，我们返回模拟数据
    return getMockResponse($data);
}

// 模拟响应数据
function getMockResponse($requestData) {
    // 根据请求的酒店ID返回不同的响应
    $hotelId = $requestData['hotelId'] ?? 100823;
    
    if ($hotelId == 100826) {
        // 模拟无可用房间的情况
        return [
            "code" => 200,
            "message" => "success",
            "data" => [
                "success" => false,
                "message" => "该酒店在指定日期无可用房间或价格",
                "hotelId" => $hotelId,
                "query_params" => [
                    "hotelId" => $hotelId,
                    "startDate" => $requestData['startDate'],
                    "endDate" => $requestData['endDate'],
                    "numRooms" => $requestData['numRooms'] ?? 1,
                    "adults" => $requestData['adults'] ?? 1,
                    "children" => $requestData['children'] ?? 0,
                    "accessCode" => $requestData['accessCode'] ?? null
                ]
            ]
        ];
    }
    
    return [
        "code" => 200,
        "message" => "success",
        "data" => [
            "success" => true,
            "hotel" => [
                "hotelId" => $hotelId,
                "memberPrice" => [
                    "currency" => "USD",
                    "originalPrice" => 22000,
                    "cnyPrice" => 158400,
                    "priceDisplay" => "¥1,584.00",
                    "rateCode" => "MEM",
                    "rateName" => "Member Rate",
                    "guaranteePolicy" => "GCC_CRD",
                    "cancelRuleString" => "可免费取消至入住前24小时"
                ],
                "memberRoom" => [
                    "roomCode" => "STD",
                    "roomName" => "标准房",
                    "roomDescription" => "舒适的标准客房，配备现代化设施"
                ],
                "nonMemberPrice" => [
                    "currency" => "USD",
                    "originalPrice" => 25000,
                    "cnyPrice" => 180000,
                    "priceDisplay" => "¥1,800.00",
                    "rateCode" => "BAR",
                    "rateName" => "Best Available Rate",
                    "guaranteePolicy" => "GCC_CRD",
                    "cancelRuleString" => "可免费取消至入住前24小时"
                ],
                "nonMemberRoom" => [
                    "roomCode" => "STD",
                    "roomName" => "标准房",
                    "roomDescription" => "舒适的标准客房，配备现代化设施"
                ],
                "priceComparison" => [
                    "memberSavings" => 21600,
                    "memberSavingsDisplay" => "¥216.00",
                    "discountPercentage" => 12.0,
                    "discountPercentageDisplay" => "12%"
                ],
                "availableRoomsCount" => 3,
                "totalRatesCount" => 5
            ],
            "query_params" => [
                "hotelId" => $hotelId,
                "startDate" => $requestData['startDate'],
                "endDate" => $requestData['endDate'],
                "numRooms" => $requestData['numRooms'] ?? 1,
                "adults" => $requestData['adults'] ?? 1,
                "children" => $requestData['children'] ?? 0,
                "accessCode" => !empty($requestData['accessCode']) ? "***" : null
            ]
        ]
    ];
}

// 处理响应数据的函数
function processHotelPrices($response) {
    if ($response['code'] !== 200) {
        echo "API调用失败: " . $response['message'] . "\n";
        return;
    }

    $data = $response['data'];
    echo "=== 酒店会员价和非会员价查询结果 ===\n";
    echo "查询成功: " . ($data['success'] ? '是' : '否') . "\n";
    echo "查询参数: " . json_encode($data['query_params'], JSON_UNESCAPED_UNICODE) . "\n\n";

    if (!$data['success']) {
        echo "查询失败: " . ($data['message'] ?? '未知错误') . "\n";
        return;
    }

    $hotel = $data['hotel'];
    echo "--- 酒店ID: " . $hotel['hotelId'] . " ---\n";
    
    // 显示会员价信息
    if (isset($hotel['memberPrice'])) {
        echo "✅ 会员价: " . $hotel['memberPrice']['priceDisplay'] . 
             " (" . $hotel['memberPrice']['rateName'] . ")\n";
        echo "   房型: " . $hotel['memberRoom']['roomName'] . "\n";
        echo "   取消政策: " . $hotel['memberPrice']['cancelRuleString'] . "\n";
    } else {
        echo "❌ 无会员价\n";
    }

    // 显示非会员价信息
    if (isset($hotel['nonMemberPrice'])) {
        echo "💰 非会员价: " . $hotel['nonMemberPrice']['priceDisplay'] . 
             " (" . $hotel['nonMemberPrice']['rateName'] . ")\n";
        echo "   房型: " . $hotel['nonMemberRoom']['roomName'] . "\n";
        echo "   取消政策: " . $hotel['nonMemberPrice']['cancelRuleString'] . "\n";
    } else {
        echo "❌ 无非会员价\n";
    }

    // 显示价格对比信息
    if (isset($hotel['priceComparison'])) {
        echo "🎉 会员优惠: " . $hotel['priceComparison']['memberSavingsDisplay'] . 
             " (节省 " . $hotel['priceComparison']['discountPercentageDisplay'] . ")\n";
    }

    echo "   可用房型数: " . $hotel['availableRoomsCount'] . "\n";
    echo "   总价格方案数: " . $hotel['totalRatesCount'] . "\n\n";
}

// 实际HTTP请求函数（示例）
function makeHttpRequest($url, $data) {
    $options = [
        'http' => [
            'header'  => "Content-type: application/json\r\n",
            'method'  => 'POST',
            'content' => json_encode($data)
        ]
    ];
    $context  = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    return json_decode($result, true);
}

// 使用示例
echo "=== 查询单个酒店最低会员价和非会员价示例 ===\n\n";

echo "测试1: 正常查询\n";
echo "================\n";
echo "请求数据:\n";
echo json_encode($requestData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// 调用API（这里使用模拟数据）
$response1 = callHotelLowestMemberAndNonMemberPricesAPI($requestData);

// 处理响应
processHotelPrices($response1);

echo "\n测试2: 无可用房间的情况\n";
echo "========================\n";
$requestData2 = array_merge($requestData, ['hotelId' => 100826]);
echo "请求数据:\n";
echo json_encode($requestData2, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

$response2 = callHotelLowestMemberAndNonMemberPricesAPI($requestData2);
processHotelPrices($response2);

// 实际使用时的代码示例（注释掉）
/*
$apiUrl = 'https://your-domain.com/api/v1/sabre/hotelLowestMemberAndNonMemberPrices';
$response = makeHttpRequest($apiUrl, $requestData);
processHotelPrices($response);
*/

echo "\n=== 示例完成 ===\n";
