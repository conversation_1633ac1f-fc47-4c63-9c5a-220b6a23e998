<?php

/**
 * 批量查询多个酒店最低会员价和非会员价接口示例
 * 
 * 该示例展示如何调用接口获取多个酒店的最低会员价和对应的非会员价
 */

// 模拟请求数据
$requestData = [
    "hotelIds" => [100823, 100824, 100825],
    "startDate" => "2025-07-09",
    "endDate" => "2025-07-11",
    "numRooms" => 1,
    "adults" => 2,
    "children" => 1,
    "childrenAge" => "8",
    "accessCode" => "SUMMER2025"
];

// 模拟API调用
function callMultipleHotelsLowestMemberAndNonMemberPricesAPI($data) {
    // 这里应该是实际的HTTP请求
    // 为了演示，我们返回模拟数据
    return getMockResponse();
}

// 模拟响应数据
function getMockResponse() {
    return [
        "code" => 200,
        "message" => "success",
        "data" => [
            "success" => true,
            "hotels" => [
                [
                    "hotelId" => 100823,
                    "memberPrice" => [
                        "currency" => "USD",
                        "originalPrice" => 22000,
                        "cnyPrice" => 158400,
                        "priceDisplay" => "¥1,584.00",
                        "rateCode" => "MEM",
                        "rateName" => "Member Rate",
                        "guaranteePolicy" => "GCC_CRD",
                        "cancelRuleString" => "可免费取消至入住前24小时"
                    ],
                    "memberRoom" => [
                        "roomCode" => "STD",
                        "roomName" => "标准房",
                        "roomDescription" => "舒适的标准客房，配备现代化设施"
                    ],
                    "nonMemberPrice" => [
                        "currency" => "USD",
                        "originalPrice" => 25000,
                        "cnyPrice" => 180000,
                        "priceDisplay" => "¥1,800.00",
                        "rateCode" => "BAR",
                        "rateName" => "Best Available Rate",
                        "guaranteePolicy" => "GCC_CRD",
                        "cancelRuleString" => "可免费取消至入住前24小时"
                    ],
                    "nonMemberRoom" => [
                        "roomCode" => "STD",
                        "roomName" => "标准房",
                        "roomDescription" => "舒适的标准客房，配备现代化设施"
                    ],
                    "priceComparison" => [
                        "memberSavings" => 21600,
                        "memberSavingsDisplay" => "¥216.00",
                        "discountPercentage" => 12.0,
                        "discountPercentageDisplay" => "12%"
                    ],
                    "availableRoomsCount" => 3,
                    "totalRatesCount" => 5
                ],
                [
                    "hotelId" => 100824,
                    "memberPrice" => [
                        "currency" => "USD",
                        "originalPrice" => 26000,
                        "cnyPrice" => 187200,
                        "priceDisplay" => "¥1,872.00",
                        "rateCode" => "MEM",
                        "rateName" => "Member Rate",
                        "guaranteePolicy" => "GCC_CRD",
                        "cancelRuleString" => "可免费取消至入住前48小时"
                    ],
                    "memberRoom" => [
                        "roomCode" => "DLX",
                        "roomName" => "豪华房",
                        "roomDescription" => "宽敞的豪华客房，享有城市景观"
                    ],
                    "nonMemberPrice" => [
                        "currency" => "USD",
                        "originalPrice" => 30000,
                        "cnyPrice" => 216000,
                        "priceDisplay" => "¥2,160.00",
                        "rateCode" => "BAR",
                        "rateName" => "Best Available Rate",
                        "guaranteePolicy" => "GCC_CRD",
                        "cancelRuleString" => "可免费取消至入住前48小时"
                    ],
                    "nonMemberRoom" => [
                        "roomCode" => "DLX",
                        "roomName" => "豪华房",
                        "roomDescription" => "宽敞的豪华客房，享有城市景观"
                    ],
                    "priceComparison" => [
                        "memberSavings" => 28800,
                        "memberSavingsDisplay" => "¥288.00",
                        "discountPercentage" => 13.33,
                        "discountPercentageDisplay" => "13.33%"
                    ],
                    "availableRoomsCount" => 2,
                    "totalRatesCount" => 4
                ],
                [
                    "hotelId" => 100825,
                    // 只有非会员价的情况
                    "nonMemberPrice" => [
                        "currency" => "USD",
                        "originalPrice" => 32000,
                        "cnyPrice" => 230400,
                        "priceDisplay" => "¥2,304.00",
                        "rateCode" => "BAR",
                        "rateName" => "Best Available Rate",
                        "guaranteePolicy" => "GCC_CRD",
                        "cancelRuleString" => "不可取消"
                    ],
                    "nonMemberRoom" => [
                        "roomCode" => "STE",
                        "roomName" => "套房",
                        "roomDescription" => "豪华套房，包含独立客厅和卧室"
                    ],
                    "availableRoomsCount" => 1,
                    "totalRatesCount" => 2
                ]
            ],
            "total_count" => 3,
            "errors" => [
                [
                    "hotelId" => 100826,
                    "error" => "该酒店在指定日期无可用房间"
                ]
            ],
            "query_params" => [
                "startDate" => "2025-07-09",
                "endDate" => "2025-07-11",
                "numRooms" => 1,
                "adults" => 2,
                "children" => 1,
                "accessCode" => "***"
            ]
        ]
    ];
}

// 处理响应数据的函数
function processHotelPrices($response) {
    if ($response['code'] !== 200) {
        echo "API调用失败: " . $response['message'] . "\n";
        return;
    }

    $data = $response['data'];
    echo "=== 酒店会员价和非会员价查询结果 ===\n";
    echo "查询成功: " . ($data['success'] ? '是' : '否') . "\n";
    echo "成功查询酒店数量: " . $data['total_count'] . "\n";
    echo "查询参数: " . json_encode($data['query_params'], JSON_UNESCAPED_UNICODE) . "\n\n";

    // 处理每个酒店的价格信息
    foreach ($data['hotels'] as $hotel) {
        echo "--- 酒店ID: " . $hotel['hotelId'] . " ---\n";
        
        // 显示会员价信息
        if (isset($hotel['memberPrice'])) {
            echo "✅ 会员价: " . $hotel['memberPrice']['priceDisplay'] . 
                 " (" . $hotel['memberPrice']['rateName'] . ")\n";
            echo "   房型: " . $hotel['memberRoom']['roomName'] . "\n";
            echo "   取消政策: " . $hotel['memberPrice']['cancelRuleString'] . "\n";
        } else {
            echo "❌ 无会员价\n";
        }

        // 显示非会员价信息
        if (isset($hotel['nonMemberPrice'])) {
            echo "💰 非会员价: " . $hotel['nonMemberPrice']['priceDisplay'] . 
                 " (" . $hotel['nonMemberPrice']['rateName'] . ")\n";
            echo "   房型: " . $hotel['nonMemberRoom']['roomName'] . "\n";
            echo "   取消政策: " . $hotel['nonMemberPrice']['cancelRuleString'] . "\n";
        } else {
            echo "❌ 无非会员价\n";
        }

        // 显示价格对比信息
        if (isset($hotel['priceComparison'])) {
            echo "🎉 会员优惠: " . $hotel['priceComparison']['memberSavingsDisplay'] . 
                 " (节省 " . $hotel['priceComparison']['discountPercentageDisplay'] . ")\n";
        }

        echo "   可用房型数: " . $hotel['availableRoomsCount'] . "\n";
        echo "   总价格方案数: " . $hotel['totalRatesCount'] . "\n\n";
    }

    // 显示错误信息
    if (!empty($data['errors'])) {
        echo "=== 查询失败的酒店 ===\n";
        foreach ($data['errors'] as $error) {
            echo "酒店ID " . $error['hotelId'] . ": " . $error['error'] . "\n";
        }
    }
}

// 实际的HTTP请求示例（使用cURL）
function makeHttpRequest($url, $data) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json',
            // 'Authorization: Bearer YOUR_TOKEN_HERE'
        ],
        CURLOPT_TIMEOUT => 30
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($response === false) {
        return ['code' => 500, 'message' => 'HTTP请求失败'];
    }
    
    return json_decode($response, true);
}

// 使用示例
echo "=== 批量查询酒店最低会员价和非会员价示例 ===\n\n";

echo "请求数据:\n";
echo json_encode($requestData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// 调用API（这里使用模拟数据）
$response = callMultipleHotelsLowestMemberAndNonMemberPricesAPI($requestData);

// 处理响应
processHotelPrices($response);

// 实际使用时的代码示例（注释掉）
/*
$apiUrl = 'https://your-domain.com/api/v1/sabre/multipleHotelsLowestMemberAndNonMemberPrices';
$response = makeHttpRequest($apiUrl, $requestData);
processHotelPrices($response);
*/

echo "\n=== 示例完成 ===\n";
