<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 批量查询配置
    |--------------------------------------------------------------------------
    |
    | 配置批量查询多个酒店最佳价格的相关参数
    |
    */

    // 批量查询限制
    'batch_limits' => [
        // 单次查询最大酒店数量
        'max_hotels_per_request' => env('SABRE_BATCH_MAX_HOTELS', 20),

        // 并发查询超时时间（秒）
        'query_timeout' => env('SABRE_BATCH_TIMEOUT', 30),

        // 单个酒店查询超时时间（秒）
        'single_hotel_timeout' => env('SABRE_SINGLE_HOTEL_TIMEOUT', 10),
    ],

    // 缓存配置
    'cache' => [
        // 批量查询结果缓存时间（分钟）
        'batch_result_ttl' => env('SABRE_BATCH_CACHE_TTL', 15),

        // 缓存键前缀
        'cache_prefix' => env('SABRE_BATCH_CACHE_PREFIX', 'sabre_batch_'),

        // 是否启用缓存
        'enabled' => env('SABRE_BATCH_CACHE_ENABLED', true),
    ],

    // 并发控制
    'concurrency' => [
        // 是否启用并发查询
        'enabled' => env('SABRE_BATCH_CONCURRENT', true),

        // 最大并发数
        'max_concurrent' => env('SABRE_BATCH_MAX_CONCURRENT', 10),

        // 批次大小（每批处理的酒店数量）
        'batch_size' => env('SABRE_BATCH_SIZE', 5),
    ],

    // 错误处理
    'error_handling' => [
        // 是否允许部分成功
        'allow_partial_success' => env('SABRE_BATCH_PARTIAL_SUCCESS', true),

        // 最大重试次数
        'max_retries' => env('SABRE_BATCH_MAX_RETRIES', 2),

        // 重试延迟（毫秒）
        'retry_delay' => env('SABRE_BATCH_RETRY_DELAY', 1000),

        // 失败率阈值（超过此比例则认为批量查询失败）
        'failure_threshold' => env('SABRE_BATCH_FAILURE_THRESHOLD', 0.8),
    ],

    // 日志配置
    'logging' => [
        // 是否记录批量查询日志
        'enabled' => env('SABRE_BATCH_LOG_ENABLED', true),

        // 是否记录查询统计
        'log_statistics' => env('SABRE_BATCH_LOG_STATS', true),

        // 是否记录慢查询
        'log_slow_queries' => env('SABRE_BATCH_LOG_SLOW', true),

        // 慢查询阈值（秒）
        'slow_query_threshold' => env('SABRE_BATCH_SLOW_THRESHOLD', 5),
    ],

    // 价格筛选配置
    'price_filtering' => [
        // 是否启用价格筛选
        'enabled' => env('SABRE_BATCH_PRICE_FILTER', true),

        // 最低价格阈值（分，低于此价格的被过滤）
        'min_price_threshold' => env('SABRE_BATCH_MIN_PRICE', 1000),

        // 最高价格阈值（分，高于此价格的被过滤）
        'max_price_threshold' => env('SABRE_BATCH_MAX_PRICE', 10000000),

        // 是否优先显示会员价格
        'prefer_member_rates' => env('SABRE_BATCH_PREFER_MEMBER', true),
    ],

    // 响应格式配置
    'response_format' => [
        // 价格显示格式
        'price_format' => [
            'currency_symbol' => '¥',
            'decimal_places' => 2,
            'thousands_separator' => ',',
        ],

        // 是否包含详细房型信息
        'include_room_details' => env('SABRE_BATCH_INCLUDE_ROOM_DETAILS', true),

        // 是否包含取消政策
        'include_cancellation_policy' => env('SABRE_BATCH_INCLUDE_CANCEL_POLICY', true),

        // 是否包含担保政策
        'include_guarantee_policy' => env('SABRE_BATCH_INCLUDE_GUARANTEE_POLICY', true),
    ],

    // 性能监控
    'monitoring' => [
        // 是否启用性能监控
        'enabled' => env('SABRE_BATCH_MONITORING', true),

        // 监控指标
        'metrics' => [
            'query_count' => true,
            'success_rate' => true,
            'average_response_time' => true,
            'cache_hit_rate' => true,
        ],

        // 性能报告间隔（分钟）
        'report_interval' => env('SABRE_BATCH_REPORT_INTERVAL', 60),
    ],

    // 默认查询参数
    'default_params' => [
        'numRooms' => 1,
        'adults' => 2,
        'children' => 0,
        'loyaltyProgram' => 'GHA',
        'loyaltyLevel' => 'RED',
        'accessType' => 'Promotion',
        'content' => 'full',
    ],

    // API限流配置
    'rate_limiting' => [
        // 是否启用限流
        'enabled' => env('SABRE_BATCH_RATE_LIMIT', true),

        // 每分钟最大请求数
        'requests_per_minute' => env('SABRE_BATCH_RPM', 60),

        // 每小时最大请求数
        'requests_per_hour' => env('SABRE_BATCH_RPH', 1000),

        // 限流键前缀
        'key_prefix' => 'sabre_batch_limit_',
    ],

    // 数据验证
    'validation' => [
        // 酒店ID验证规则
        'hotel_id_rules' => [
            'min' => 1,
            'max' => 999999999,
        ],

        // 日期验证规则
        'date_rules' => [
            'max_advance_days' => 365, // 最多提前预订天数
            'max_stay_days' => 30,     // 最长住宿天数
        ],

        // 客人数量验证
        'guest_rules' => [
            'max_adults' => 20,
            'max_children' => 10,
            'max_rooms' => 10,
        ],
    ],
];
