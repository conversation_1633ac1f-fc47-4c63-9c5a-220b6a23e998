# 酒店最低会员价和非会员价查询功能

## 功能概述

这个功能为酒店详情页面和列表页面提供了一个专门的接口，用于查询单个酒店的最低会员价和对应的最低非会员价，并提供价格对比信息。这对于展示会员优惠、促进用户注册会员具有重要意义。

## 主要特性

### 🎯 核心功能
- **单酒店查询**: 针对单个酒店进行精确查询
- **价格对比**: 自动计算会员价与非会员价的差异和折扣百分比
- **灵活响应**: 支持只有会员价、只有非会员价或两者都有的情况
- **实时数据**: 从Sabre API实时获取最新价格信息

### 🚀 性能优化
- **缓存机制**: 查询结果自动缓存，提高响应速度
- **智能查询**: 根据是否有促销代码选择最优查询方式
- **错误处理**: 优雅处理各种异常情况

### 📊 数据完整性
- **房型信息**: 包含对应价格的房型详细信息
- **政策信息**: 提供取消政策、担保政策等重要信息
- **货币转换**: 自动转换为人民币显示，支持原始货币保留

## 接口信息

### 请求地址
```
POST /api/v1/sabre/hotelLowestMemberAndNonMemberPrices
```

### 核心参数
- `hotelId`: 酒店ID（必需）
- `startDate`: 入住日期（必需）
- `endDate`: 离店日期（必需）
- `numRooms`: 房间数量（可选，默认1）
- `adults`: 成人数量（可选，默认1）
- `children`: 儿童数量（可选，默认0）
- `accessCode`: 促销代码（可选）

## 响应数据结构

### 成功响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "success": true,
        "hotel": {
            "hotelId": 100823,
            "memberPrice": {
                "cnyPrice": 158400,
                "priceDisplay": "¥1,584.00",
                "rateName": "Member Rate"
            },
            "nonMemberPrice": {
                "cnyPrice": 180000,
                "priceDisplay": "¥1,800.00",
                "rateName": "Best Available Rate"
            },
            "priceComparison": {
                "memberSavings": 21600,
                "memberSavingsDisplay": "¥216.00",
                "discountPercentage": 12.0,
                "discountPercentageDisplay": "12%"
            }
        },
        "query_params": {
            "hotelId": 100823,
            "startDate": "2025-07-09",
            "endDate": "2025-07-11"
        }
    }
}
```

### 无可用房间响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "success": false,
        "message": "该酒店在指定日期无可用房间或价格",
        "hotelId": 100823,
        "query_params": {
            "hotelId": 100823,
            "startDate": "2025-07-09",
            "endDate": "2025-07-11"
        }
    }
}
```

## 实现细节

### 文件结构
```
app/
├── Http/Controllers/V1/SabreController.php          # 控制器新增方法
├── Services/SabreAvailabilityService.php           # 服务层新增方法
routes/
├── v1.php                                          # 路由配置
docs/
├── api/hotel-lowest-member-nonmember-prices.md     # API文档
examples/
├── hotel_lowest_member_nonmember_prices_example.php  # 使用示例
tests/
├── Feature/HotelLowestMemberNonMemberPricesTest.php   # 功能测试
```

### 核心方法

#### SabreController::checkHotelLowestMemberAndNonMemberPrices()
- 处理HTTP请求和参数验证
- 调用服务层方法获取数据
- 记录查询日志和统计信息
- 统一错误处理和响应格式化

#### SabreAvailabilityService::checkHotelLowestMemberAndNonMemberPrices()
- 查询单个酒店的价格信息
- 根据是否有促销代码选择查询方式
- 优雅的错误处理机制

#### SabreAvailabilityService::extractLowestMemberAndNonMemberPrices()
- 从查询结果中提取最低会员价和非会员价
- 计算价格差异和折扣百分比
- 构建标准化的响应数据结构

## 使用场景

### 1. 酒店详情页面
```javascript
// 前端调用示例
const searchParams = {
    hotelId: 100823,
    startDate: '2025-07-09',
    endDate: '2025-07-11',
    adults: 2
};

fetch('/api/v1/sabre/hotelLowestMemberAndNonMemberPrices', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(searchParams)
})
.then(response => response.json())
.then(data => {
    if (data.data.success) {
        // 显示会员优惠信息
        displayMemberBenefits(data.data.hotel);
    } else {
        // 处理无可用房间的情况
        showNoRoomsMessage(data.data.message);
    }
});
```

### 2. 酒店列表页面
- 为每个酒店单独调用接口获取会员优惠信息
- 在酒店卡片上显示"会员可省¥216"
- 突出显示折扣百分比"会员享12%优惠"

### 3. 会员权益展示
- 实时显示会员价格优势
- 引导用户注册会员
- 提供价格对比功能

## 测试

### 运行测试
```bash
# 运行功能测试
php artisan test tests/Feature/HotelLowestMemberNonMemberPricesTest.php

# 运行所有相关测试
php artisan test --filter="HotelLowest"
```

### 测试覆盖
- ✅ 正常查询流程
- ✅ 参数验证（必需参数、日期验证）
- ✅ 儿童年龄验证
- ✅ 服务异常处理
- ✅ 无可用房间的处理

## 性能建议

### 查询优化
1. **缓存利用**: 相同参数的查询会使用缓存，响应更快
2. **客户端缓存**: 建议前端实现适当的缓存机制
3. **按需查询**: 只在用户真正需要时才调用接口

### 监控指标
- 查询成功率
- 平均响应时间
- 缓存命中率
- 错误类型分布

## 配置说明

### 相关配置文件
- `config/sabre.php`: Sabre API基础配置

### 重要配置项
```php
// 查询配置
'query_settings' => [
    'query_timeout' => 30,           // 查询超时时间
    'retry_attempts' => 3,           // 重试次数
],

// 缓存配置
'cache' => [
    'result_ttl' => 15,              // 缓存时间（分钟）
    'enabled' => true,               // 是否启用缓存
],
```

## 注意事项

1. **数据完整性**: 某些酒店可能只有会员价或只有非会员价
2. **价格实时性**: 价格信息实时从Sabre API获取，可能存在延迟
3. **错误处理**: 查询失败会返回明确的错误信息
4. **限流机制**: 请注意API调用频率限制
5. **缓存策略**: 合理利用缓存机制提高性能

## 后续扩展

### 可能的功能增强
- 支持更多价格类型（企业价、团队价等）
- 增加价格历史趋势分析
- 支持价格预警和监控
- 增加更详细的房型对比信息
- 支持批量查询多个酒店（如果业务需要）

### 技术优化
- 实现更智能的缓存策略
- 增加查询结果的数据分析
- 优化查询性能
- 增加更完善的监控和告警机制
