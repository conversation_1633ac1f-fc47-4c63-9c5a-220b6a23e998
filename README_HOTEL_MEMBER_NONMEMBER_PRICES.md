# 酒店最低会员价和非会员价查询功能

## 功能概述

这个功能为酒店列表页面提供了一个专门的接口，用于批量查询多个酒店的最低会员价和对应的最低非会员价，并提供价格对比信息。这对于展示会员优惠、促进用户注册会员具有重要意义。

## 主要特性

### 🎯 核心功能
- **批量查询**: 一次请求可查询最多20个酒店
- **价格对比**: 自动计算会员价与非会员价的差异和折扣百分比
- **灵活响应**: 支持只有会员价、只有非会员价或两者都有的情况
- **错误容错**: 单个酒店查询失败不影响其他酒店的结果

### 🚀 性能优化
- **缓存机制**: 查询结果自动缓存，提高响应速度
- **并发查询**: 多个酒店并发查询，减少总响应时间
- **智能限流**: 内置请求频率限制，保护系统稳定性

### 📊 数据完整性
- **房型信息**: 包含对应价格的房型详细信息
- **政策信息**: 提供取消政策、担保政策等重要信息
- **货币转换**: 自动转换为人民币显示，支持原始货币保留

## 接口信息

### 请求地址
```
POST /api/v1/sabre/multipleHotelsLowestMemberAndNonMemberPrices
```

### 核心参数
- `hotelIds`: 酒店ID列表（必需，最多20个）
- `startDate`: 入住日期（必需）
- `endDate`: 离店日期（必需）
- `numRooms`: 房间数量（可选，默认1）
- `adults`: 成人数量（可选，默认1）
- `children`: 儿童数量（可选，默认0）
- `accessCode`: 促销代码（可选）

## 响应数据结构

### 成功响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "success": true,
        "hotels": [
            {
                "hotelId": 100823,
                "memberPrice": {
                    "cnyPrice": 158400,
                    "priceDisplay": "¥1,584.00",
                    "rateName": "Member Rate"
                },
                "nonMemberPrice": {
                    "cnyPrice": 180000,
                    "priceDisplay": "¥1,800.00",
                    "rateName": "Best Available Rate"
                },
                "priceComparison": {
                    "memberSavings": 21600,
                    "memberSavingsDisplay": "¥216.00",
                    "discountPercentage": 12.0,
                    "discountPercentageDisplay": "12%"
                }
            }
        ],
        "total_count": 1,
        "errors": []
    }
}
```

## 实现细节

### 文件结构
```
app/
├── Http/Controllers/V1/SabreController.php          # 控制器新增方法
├── Services/SabreAvailabilityService.php           # 服务层新增方法
routes/
├── v1.php                                          # 路由配置
docs/
├── api/multiple-hotels-lowest-member-nonmember-prices.md  # API文档
examples/
├── multiple_hotels_lowest_member_nonmember_prices_example.php  # 使用示例
tests/
├── Feature/MultipleHotelsLowestMemberNonMemberPricesTest.php   # 功能测试
```

### 核心方法

#### SabreController::checkMultipleHotelsLowestMemberAndNonMemberPrices()
- 处理HTTP请求和参数验证
- 调用服务层方法获取数据
- 记录查询日志和统计信息
- 统一错误处理和响应格式化

#### SabreAvailabilityService::checkMultipleHotelsLowestMemberAndNonMemberPrices()
- 批量查询多个酒店的价格信息
- 并发处理提高查询效率
- 错误容错机制

#### SabreAvailabilityService::extractLowestMemberAndNonMemberPrices()
- 从查询结果中提取最低会员价和非会员价
- 计算价格差异和折扣百分比
- 构建标准化的响应数据结构

## 使用场景

### 1. 酒店列表页面
```javascript
// 前端调用示例
const hotelIds = [100823, 100824, 100825];
const searchParams = {
    hotelIds,
    startDate: '2025-07-09',
    endDate: '2025-07-11',
    adults: 2
};

fetch('/api/v1/sabre/multipleHotelsLowestMemberAndNonMemberPrices', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(searchParams)
})
.then(response => response.json())
.then(data => {
    // 渲染酒店列表，显示会员优惠信息
    renderHotelList(data.data.hotels);
});
```

### 2. 会员权益展示
- 在酒店卡片上显示"会员可省¥216"
- 突出显示折扣百分比"会员享12%优惠"
- 引导用户注册会员

### 3. 价格对比功能
- 并排显示会员价和非会员价
- 高亮显示节省金额
- 提供价格趋势分析

## 测试

### 运行测试
```bash
# 运行功能测试
php artisan test tests/Feature/MultipleHotelsLowestMemberNonMemberPricesTest.php

# 运行所有相关测试
php artisan test --filter="MultipleHotels"
```

### 测试覆盖
- ✅ 正常查询流程
- ✅ 参数验证（必需参数、数量限制、日期验证）
- ✅ 儿童年龄验证
- ✅ 服务异常处理
- ✅ 部分查询失败的容错处理

## 性能建议

### 查询优化
1. **批量大小**: 建议单次查询控制在10个酒店以内
2. **缓存利用**: 相同参数的查询会使用缓存，响应更快
3. **客户端缓存**: 建议前端实现适当的缓存机制

### 监控指标
- 查询成功率
- 平均响应时间
- 缓存命中率
- 错误类型分布

## 配置说明

### 相关配置文件
- `config/sabre.php`: Sabre API基础配置
- `config/sabre_batch_query.php`: 批量查询专用配置

### 重要配置项
```php
// 批量查询限制
'batch_limits' => [
    'max_hotels_per_request' => 20,  // 单次最大酒店数
    'query_timeout' => 30,           // 查询超时时间
],

// 缓存配置
'cache' => [
    'batch_result_ttl' => 15,        // 缓存时间（分钟）
    'enabled' => true,               // 是否启用缓存
],
```

## 注意事项

1. **数据完整性**: 某些酒店可能只有会员价或只有非会员价
2. **价格实时性**: 价格信息实时从Sabre API获取，可能存在延迟
3. **错误处理**: 单个酒店查询失败不会影响整体结果
4. **限流机制**: 请注意API调用频率限制
5. **缓存策略**: 合理利用缓存机制提高性能

## 后续扩展

### 可能的功能增强
- 支持更多价格类型（企业价、团队价等）
- 增加价格历史趋势分析
- 支持价格预警和监控
- 增加更详细的房型对比信息

### 技术优化
- 实现更智能的缓存策略
- 增加查询结果的数据分析
- 优化并发查询的性能
- 增加更完善的监控和告警机制
