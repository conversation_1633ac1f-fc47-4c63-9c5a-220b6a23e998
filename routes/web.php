<?php

use App\Http\Controllers\AboutController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\ContinentController;
use App\Http\Controllers\DiscountController;
use App\Http\Controllers\HotelController;
use App\Http\Controllers\IndexController;
use App\Http\Controllers\MembershipController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::get('/welcome', function () {
    return view('welcome');
});

Route::get('/', [IndexController::class, 'index'])->name("index");

//需要登录的操作
Route::group(['middleware' => 'auth'], function () {
    Route::get('/logout', [IndexController::class, 'logout'])->name("logout");
});


Route::get('/destination',[ContinentController::class, 'destination'])->name("destination");
//登录相关
Route::prefix("auth")->group(function () {
    Route::get('/login',[AuthController::class, 'login'])->name("auth.login");
    Route::get('/register',[AuthController::class, 'register'])->name("auth.register");
    Route::get('/active',[AuthController::class, 'active'])->name("auth.active");
    Route::get('/reset-password',[AuthController::class, 'resetPassword'])->name("auth.resetPassword");
    Route::get('/reset-password-confirm',[AuthController::class, 'resetPasswordConfirm'])->name("auth.resetPasswordConfirm");
    Route::get('/forget-account',[AuthController::class, 'forgetAccount'])->name("auth.forgetAccount");

    Route::post('/loginUser',[AuthController::class, 'loginUser'])->name("auth.loginUser");
    Route::post('/register', [AuthController::class, 'registerUser'])->name("auth.registerUser");//注册
    Route::post('/reset_pwd', [AuthController::class, 'resetPwd'])->name("auth.resetPwd");//发送找回密码邮件
    Route::post('/set_pwd', [AuthController::class, 'setPwd'])->name("auth.setPwd");//重置密码
    Route::post('/active', [AuthController::class, 'activeU'])->name("auth.activeUser");//账号激活
    Route::post('/find_member', [AuthController::class, 'findMember'])->name("auth.findMember");//找回会员号
});


//用户相关
Route::prefix("user")->group(function () {
    Route::get('/dashboard',[UserController::class, 'dashboard'])->name("user.dashboard");
    Route::get('/orders',[UserController::class, 'orders'])->name("user.orders");
    Route::get('/favorite',[UserController::class, 'favorite'])->name("user.favorite");
    Route::get('/profile',[UserController::class, 'profile'])->name("user.profile");
    Route::get('/settings',[UserController::class, 'settings'])->name("user.settings");
    Route::get('/reset-password',[UserController::class, 'resetPassword'])->name("user.reset-password");
});


Route::group(['middleware' => 'auth:web'], function () {
    Route::post('/logout',  [AuthController::class, 'logout']);//退出登录
    //个人信息
    Route::get('/user_info', [AuthController::class, 'userInfo']);
    //修改密码
    Route::post('/up_pwd', [AuthController::class, 'upPwd']);
});


//关于
Route::prefix("about")->group(function () {
    Route::get('/index',[AboutController::class, 'index'])->name("about.index");
    Route::get('/brands',[AboutController::class, 'brands'])->name("about.brands");
    Route::get('/brands/{id}',[AboutController::class, 'brandsDetail'])->name("about.brands-detail");
    Route::get('/partners',[AboutController::class, 'partners'])->name("about.partners");
    Route::get('/news',[AboutController::class, 'news'])->name("about.news");
    Route::get('/news/{id}',[AboutController::class, 'newsDetail'])->name("about.news-detail");
    Route::get('/wechat-news', [AboutController::class, 'news'])->name("about.wechat-news")->defaults('source', 'wechat_list');
    Route::get('/regent-seven-seas-cruises',[AboutController::class, 'regentSeven'])->name("about.regent-seven-seas-cruises");
});

//会员相关
Route::prefix("membership")->group(function () {
    Route::get('/index',[MembershipController::class, 'index'])->name("membership.index");
    Route::get('/d-plan',[MembershipController::class, 'dPlan'])->name("membership.d-plan");
    Route::get('/rights',[MembershipController::class, 'rights'])->name("membership.rights");
});

//酒店
Route::prefix("hotel")->group(function () {
    Route::get('/list',[HotelController::class, 'list'])->name("hotel.list");
    Route::get('/{id}',[HotelController::class, 'detail'])->name("hotel.hotel-detail");
});

//预约
Route::prefix("booking")->group(function () {
    Route::get('/select-room',[BookingController::class, 'selectRoom'])->name("booking.select-room");
    Route::get('/input-order-detail',[BookingController::class, 'inputOrderDetail'])->name("booking.input-order-detail");
    Route::get('/confirm-order',[BookingController::class, 'confirmOrder'])->name("booking.confirm-order");
    Route::get('/booking-success',[BookingController::class, 'bookingSuccess'])->name("booking.booking-success");
    Route::get('/order-detail',[BookingController::class, 'orderDetail'])->name("booking.order-detail");
});

//折扣相关
Route::prefix("offer")->group(function () {
    Route::get('/',[DiscountController::class, 'index'])->name("offer.index");
//     Route::get('/search',[DiscountController::class, 'search'])->name("offer.search");
    Route::get('/{id}',[DiscountController::class, 'detail'])->name("offer.detail");
});

//搜索
Route::prefix("search")->group(function () {
    Route::get('/{slug}',[HotelController::class, 'search'])->name("search.index");
});

//活动专题页
Route::prefix("activity")->group(function () {
    Route::get('/{id}',[AboutController::class, 'activityDetail'])->name("activity.detail");
});

Route::get('/sitemap', [AboutController::class, 'sitemap'])->name("sitemap");





